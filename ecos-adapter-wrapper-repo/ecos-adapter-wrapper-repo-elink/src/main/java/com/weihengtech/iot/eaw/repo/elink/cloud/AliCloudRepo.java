package com.weihengtech.iot.eaw.repo.elink.cloud;

import com.aliyun.iot20180120.Client;
import com.aliyun.iot20180120.models.*;
import com.weihengtech.iot.eaw.repo.elink.autoconfig.WrapperRepoELinkProperties;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;

public class AliCloudRepo {
    private final Client client;
    private final WrapperRepoELinkProperties properties;
    public AliCloudRepo(Client client, WrapperRepoELinkProperties properties) {
        this.client = client;
        this.properties = properties;
    }

    public List<String> queryAllOnlineDevice() throws Exception {
        final Integer pageSize = 40;
        final Integer status = 1;
        List<String> onlineDevices = new ArrayList<>();
        QueryDeviceByStatusRequest queryDeviceByStatusRequest = new QueryDeviceByStatusRequest();
        if (StringUtils.hasLength(properties.getInstanceId())) queryDeviceByStatusRequest.setIotInstanceId(properties.getInstanceId());
        queryDeviceByStatusRequest.setProductKey(properties.getProductKey());
        queryDeviceByStatusRequest.setStatus(status);
        queryDeviceByStatusRequest.setCurrentPage(1);
        queryDeviceByStatusRequest.setPageSize(pageSize);
        QueryDeviceByStatusResponse response = client.queryDeviceByStatus(queryDeviceByStatusRequest);

        QueryDeviceByStatusResponseBody body = response.getBody();
        Integer pageCount = body.getPageCount();
        body.getData().getSimpleDeviceInfo().parallelStream().forEach(info -> onlineDevices.add(info.getDeviceName()));

        for (int i = 2; i <= pageCount; i++) {
            QueryDeviceByStatusRequest statusRequest = new QueryDeviceByStatusRequest();
            if (StringUtils.hasLength(properties.getInstanceId())) statusRequest.setIotInstanceId(properties.getInstanceId());
            statusRequest.setProductKey(properties.getProductKey());
            statusRequest.setStatus(status);
            statusRequest.setCurrentPage(i);
            statusRequest.setPageSize(pageSize);
            response = client.queryDeviceByStatus(statusRequest);
            response.getBody().getData().getSimpleDeviceInfo().parallelStream().forEach(info -> onlineDevices.add(info.getDeviceName()));
        }
        return onlineDevices;
    }

    public Boolean isOnline(String deviceId) throws Exception {
        final String online = "ONLINE";
        GetDeviceStatusRequest getDeviceStatusRequest = new GetDeviceStatusRequest();
        getDeviceStatusRequest.setDeviceName(deviceId);
        getDeviceStatusRequest.setProductKey(properties.getProductKey());
        if (StringUtils.hasLength(properties.getInstanceId())) getDeviceStatusRequest.setIotInstanceId(properties.getInstanceId());

        GetDeviceStatusResponse response = client.getDeviceStatus(getDeviceStatusRequest);
        if (response.getBody().getSuccess()) {
            String status = response.getBody().getData().getStatus();
            return online.equals(status);
        }
        return false;
    }

    public Object querySingleDeviceDetail(String deviceId) throws Exception {
        QueryDeviceDetailRequest queryDeviceDetailRequest = new QueryDeviceDetailRequest();
        queryDeviceDetailRequest.setDeviceName(deviceId);
        queryDeviceDetailRequest.setProductKey(properties.getProductKey());
        if (StringUtils.hasLength(properties.getInstanceId())) queryDeviceDetailRequest.setIotInstanceId(properties.getInstanceId());
        return client.queryDeviceDetail(queryDeviceDetailRequest).getBody().getData();
    }

    public List<Object> queryBatchDeviceDetail(List<String> deviceIds) throws Exception {
        BatchQueryDeviceDetailRequest batchQueryDeviceDetailRequest = new BatchQueryDeviceDetailRequest();
        batchQueryDeviceDetailRequest.setDeviceName(deviceIds);
        batchQueryDeviceDetailRequest.setProductKey(properties.getProductKey());
        if (StringUtils.hasLength(properties.getInstanceId())) batchQueryDeviceDetailRequest.setIotInstanceId(properties.getInstanceId());
        return new ArrayList<>(client.batchQueryDeviceDetail(batchQueryDeviceDetailRequest).getBody().getData().getData());
    }

    public void reset(String deviceId) throws Exception {
        ResetThingRequest resetThingRequest = new ResetThingRequest();
        resetThingRequest.setDeviceName(deviceId);
        resetThingRequest.setProductKey(properties.getProductKey());
        if (StringUtils.hasLength(properties.getInstanceId())) resetThingRequest.setIotInstanceId(properties.getInstanceId());
        client.resetThing(resetThingRequest);
    }
}
