package com.weihengtech.iot.eaw.repo.elink.autoconfig;

import org.springframework.boot.context.properties.ConfigurationProperties;

@ConfigurationProperties(prefix = "repo.elink")
public class WrapperRepoELinkProperties {

    /**
     * 易联服务地址
     */
    private String url;

    /**
     * 阿里云accessKey
     */
    private String accessKey;

    /**
     * 阿里云accessSecret
     */
    private String accessSecret;

    /**
     * 阿里云接入端点
     */
    private String endpoint;

    /**
     * 阿里云地区id
     */
    private String regionId;

    /**
     * 阿里云产品id
     */
    private String productKey;

    /**
     * 阿里云实例id
     */
    private String instanceId;

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getAccessKey() {
        return accessKey;
    }

    public void setAccessKey(String accessKey) {
        this.accessKey = accessKey;
    }

    public String getAccessSecret() {
        return accessSecret;
    }

    public void setAccessSecret(String accessSecret) {
        this.accessSecret = accessSecret;
    }

    public String getEndpoint() {
        return endpoint;
    }

    public void setEndpoint(String endpoint) {
        this.endpoint = endpoint;
    }

    public String getRegionId() {
        return regionId;
    }

    public void setRegionId(String regionId) {
        this.regionId = regionId;
    }

    public String getProductKey() {
        return productKey;
    }

    public void setProductKey(String productKey) {
        this.productKey = productKey;
    }

    public String getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(String instanceId) {
        this.instanceId = instanceId;
    }
}