package com.weihengtech.iot.eaw.repo.elink.autoconfig;

import com.aliyun.iot20180120.Client;
import com.aliyun.teaopenapi.models.Config;
import com.weihengtech.iot.eaw.repo.elink.cloud.AliCloudRepo;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;

@EnableConfigurationProperties(WrapperRepoELinkProperties.class)
@EnableFeignClients(basePackages = "com.weihengtech.iot.eaw.repo.elink.feign")
public class WrapperRepoELinkAutoConfiguration {

    @Bean
    public Client iotClient(WrapperRepoELinkProperties properties) throws Exception {
        Config config = new Config();
        config.regionId = properties.getRegionId();
        config.accessKeyId = properties.getAccessKey();
        config.accessKeySecret = properties.getAccessSecret();
        return new Client(config);
    }

    @Bean
    public AliCloudRepo aliCloudRepo(Client client, WrapperRepoELinkProperties properties) {
        return new AliCloudRepo(client, properties);
    }
}
