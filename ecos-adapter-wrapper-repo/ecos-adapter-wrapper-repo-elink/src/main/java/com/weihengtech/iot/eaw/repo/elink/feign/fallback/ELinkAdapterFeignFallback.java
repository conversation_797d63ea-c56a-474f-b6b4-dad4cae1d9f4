package com.weihengtech.iot.eaw.repo.elink.feign.fallback;

import cn.hutool.json.JSONObject;
import com.weihengtech.iot.eaw.repo.elink.feign.ELinkAdapterFeign;
import com.weihengtech.iot.eaw.repo.elink.model.dto.ELinkParamDto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
public class ELinkAdapterFeignFallback implements ELinkAdapterFeign {

    private final Logger logger = LoggerFactory.getLogger(ELinkAdapterFeignFallback.class);

    @Override
    public JSONObject upgrade(ELinkParamDto eLinkParamDto) {
        logger.warn("ELinkAdapterFeignFallback#upgradeDevice => " + eLinkParamDto.getArgs());
        return fail();
    }

    @Override
    public JSONObject speedup(ELinkParamDto eLinkParamDto) {
        logger.warn("ELinkAdapterFeignFallback#speedupDevice => " + eLinkParamDto.getArgs());
        return fail();
    }

    @Override
    public JSONObject read(ELinkParamDto eLinkParamDto) {
        logger.warn("ELinkAdapterFeignFallback#readDevice => " + eLinkParamDto.getArgs());
        return fail();
    }

    @Override
    public JSONObject readWithFuncCode(ELinkParamDto eLinkParamDto) {
        logger.warn("ELinkAdapterFeignFallback#readDeviceWithFuncCode => " + eLinkParamDto.getArgs());
        return fail();
    }

    @Override
    public JSONObject write(ELinkParamDto eLinkParamDto) {
        logger.warn("ELinkAdapterFeignFallback#writeDevice => " + eLinkParamDto.getArgs());
        return fail();
    }

    private JSONObject fail() {
        JSONObject jsonObject = new JSONObject();
        jsonObject.set("state", "FAILURE");
        jsonObject.set("result", "false");
        return jsonObject;
    }
}
