package com.weihengtech.iot.eaw.repo.elink.feign;

import cn.hutool.json.JSONObject;
import com.weihengtech.iot.eaw.repo.elink.feign.fallback.ELinkAdapterFeignFallback;
import com.weihengtech.iot.eaw.repo.elink.model.dto.ELinkParamDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name = "ELinkAdapterFeign", url = "${repo.elink.url}", dismiss404 = true, fallback = ELinkAdapterFeignFallback.class)
public interface ELinkAdapterFeign {

    @PostMapping("/api/task/async-apply/aliyun_to_e_linker_bridge.data_tasks.set_one_device_active")
    JSONObject speedup(@RequestBody ELinkParamDto eLinkParamDto);

    @PostMapping("/api/task/apply/aliyun_to_e_linker_bridge.data_tasks.transparent_read_data")
    JSONObject read(@RequestBody ELinkParamDto eLinkParamDto);

    @PostMapping("/api/task/apply/aliyun_to_e_linker_bridge.data_tasks.transparent_read_data_with_func_code")
    JSONObject readWithFuncCode(@RequestBody ELinkParamDto eLinkParamDto);

    @PostMapping("/api/task/apply/aliyun_to_e_linker_bridge.data_tasks.transparent_write_data")
    JSONObject write(@RequestBody ELinkParamDto eLinkParamDto);

    @PostMapping("/api/task/apply/aliyun_to_e_linker_bridge.update_tasks.update_one_device_user_firmware")
    JSONObject upgrade(@RequestBody ELinkParamDto eLinkParamDto);
}
