package com.weihengtech.iot.eaw.repo.tuya.feign.fallback;

import cn.hutool.json.JSONUtil;
import com.weihengtech.iot.eaw.repo.tuya.feign.TuyaAdapterFeign;
import com.weihengtech.iot.eaw.repo.tuya.model.dto.*;
import com.weihengtech.pub.infra.resp.model.R;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
public class TuyaAdapterFeignFallback implements TuyaAdapterFeign {

    private final Logger logger = LoggerFactory.getLogger(TuyaAdapterFeignFallback.class);

    @Override
    public R speedup(TuyaSpeedupDeviceDto tuyaSpeedupDeviceDto) {
        logger.warn("TuyaAdapterFeignFallback#speedupDevice => " + tuyaSpeedupDeviceDto.getDeviceId());
        return R.Companion.fail();
    }

    @Override
    public R querySingleDeviceDetail(String deviceId) {
        logger.warn("TuyaAdapterFeignFallback#getInfoByDeviceId => " + deviceId);
        return R.Companion.fail();
    }

    @Override
    public R queryBatchDeviceDetail(String deviceIds) {
        logger.warn("TuyaAdapterFeignFallback#getBatchInfo => " + deviceIds);
        return R.Companion.fail();
    }

    @Override
    public R querySingleDeviceProperty(String deviceId) {
        logger.warn("TuyaAdapterFeignFallback#querySingleDeviceProperty => " + deviceId);
        return R.Companion.fail();
    }

    @Override
    public R queryBatchDeviceProperty(String deviceIds) {
        logger.warn("TuyaAdapterFeignFallback#queryBatchDeviceProperty => " + deviceIds);
        return R.Companion.fail();
    }

    @Override
    public R read(TuyaReadDeviceDto tuyaReadDeviceDto) {
        logger.warn("TuyaAdapterFeignFallback#readFromDevice => " + tuyaReadDeviceDto.getDeviceId());
        return R.Companion.fail();
    }

    @Override
    public R readWithFuncCode(TuyaReadDeviceWithFuncDto tuyaReadDeviceWithFuncDto) {
        logger.warn("TuyaAdapterFeignFallback#readFromDeviceWithFuncCode => " + tuyaReadDeviceWithFuncDto.getDeviceId());
        return R.Companion.fail();
    }

    @Override
    public R write(TuyaWriteDeviceDto tuyaWriteDeviceDto) {
        logger.warn("TuyaAdapterFeignFallback#writeToDevice => " + tuyaWriteDeviceDto.getDeviceId());
        return R.Companion.fail();
    }

    @Override
    public R upgrade(TuyaUpgradeDeviceDto tuyaUpgradeDeviceDto) {
        logger.warn("TuyaAdapterFeignFallback#deviceUpgrade => " + tuyaUpgradeDeviceDto.getDeviceId());
        return R.Companion.fail();
    }

    @Override
    public R reset(String deviceId) {
        logger.warn("TuyaAdapterFeignFallback#reset => " + deviceId);
        return R.Companion.fail();
    }

    @Override
    public R queryAllOnlineDevice() {
        logger.warn("TuyaAdapterFeignFallback#queryAllOnlineDevice");
        return R.Companion.fail();
    }

    @Override
    public R sendCommands(TuyaCommandsDto tuyaCommandsDto) {
        logger.warn("TuyaAdapterFeignFallback#sendCommands");
        return R.Companion.fail();
    }

    @Override
    public R addHomeMember(Long homeId, TuyaAddMemberDto tuyaAddMemberDto) {
        logger.warn("TuyaAdapterFeignFallback#addHomeMember => " + homeId + " " + JSONUtil.toJsonStr(tuyaAddMemberDto));
        return R.Companion.fail();
    }

    @Override
    public R queryHomeMembers(Long homeId) {
        logger.warn("TuyaAdapterFeignFallback#queryHomeMembers => " + homeId);
        return R.Companion.fail();
    }

    @Override
    public R deleteHomeMember(Long homeId, String uid) {
        logger.warn("TuyaAdapterFeignFallback#deleteHomeMember => " + homeId + " " + uid);
        return R.Companion.fail();
    }

    @Override
    public R queryHomeDeviceDetail(String tuyaId) {
        logger.warn("TuyaAdapterFeignFallback#queryHomeDeviceDetail => " + tuyaId);
        return R.Companion.fail();
    }

    @Override
    public R queryDeviceLog(String id, String type, Long startTime, Long endTime, Integer queryType, String startRowKey, Integer size, String codes) {
        logger.warn("TuyaAdapterFeignFallback#queryDeviceLog => " + id);
        return R.Companion.fail();
    }
}
