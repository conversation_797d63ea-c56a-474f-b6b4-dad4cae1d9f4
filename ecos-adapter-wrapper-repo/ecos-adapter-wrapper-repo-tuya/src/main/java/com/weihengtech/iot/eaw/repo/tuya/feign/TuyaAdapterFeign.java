package com.weihengtech.iot.eaw.repo.tuya.feign;

import com.weihengtech.iot.eaw.repo.tuya.feign.fallback.TuyaAdapterFeignFallback;
import com.weihengtech.iot.eaw.repo.tuya.model.dto.*;
import com.weihengtech.pub.infra.resp.model.R;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

@FeignClient(name = "TuyaAdapterFeign", url = "${repo.tuya.url}", dismiss404 = true, fallback = TuyaAdapterFeignFallback.class)
public interface TuyaAdapterFeign {
    @PostMapping("/v1/device/speedup")
    R speedup(@RequestBody TuyaSpeedupDeviceDto tuyaSpeedupDeviceDto);
    @GetMapping("/v2/devices/{id}/info")
    R querySingleDeviceDetail(@PathVariable("id") String deviceId);
    @GetMapping("/v2/devices/info")
    R queryBatchDeviceDetail(@RequestParam("ids") String deviceIds);
    @GetMapping("/v2/devices/{id}/property")
    R querySingleDeviceProperty(@PathVariable("id") String deviceId);
    @GetMapping("/v2/devices/property")
    R queryBatchDeviceProperty(@RequestParam("ids") String deviceIds);
    @PostMapping("/v1/device/read")
    R read(@RequestBody TuyaReadDeviceDto tuyaReadDeviceDto);
    @PostMapping("/v1/device/readWithFuncCode")
    R readWithFuncCode(@RequestBody TuyaReadDeviceWithFuncDto tuyaReadDeviceWithFuncDto);
    @PostMapping("/v1/device/write")
    R write(@RequestBody TuyaWriteDeviceDto tuyaWriteDeviceDto);
    @PostMapping("/v1/device/upgrade")
    R upgrade(@RequestBody TuyaUpgradeDeviceDto tuyaUpgradeDeviceDto);
    @PostMapping("/v1/device/reset")
    R reset(@RequestParam("deviceId") String deviceId);
    @GetMapping("/v1/device/allOnlineDevice")
    R queryAllOnlineDevice();
    @PostMapping("/v2/devices/commands")
    R sendCommands(@RequestBody TuyaCommandsDto tuyaCommandsDto);
    @PostMapping("/v2/homes/addMembers/{homeId}")
    R addHomeMember(@PathVariable("homeId") Long homeId, @RequestBody TuyaAddMemberDto tuyaAddMemberDto);
    @GetMapping("/v2/homes/getHomeUserList/{homeId}")
    R queryHomeMembers(@PathVariable("homeId") Long homeId);
    @DeleteMapping("/v2/homes/deleteHomeUser/{homeId}/{uid}")
    R deleteHomeMember(@PathVariable("homeId") Long homeId, @PathVariable("uid") String uid);
    @GetMapping("/v2/homes/devices/{id}")
    R queryHomeDeviceDetail(@PathVariable("id") String tuyaId);
    @GetMapping("/v2/devices/logs")
    R queryDeviceLog(
            @RequestParam("id") String id,
            @RequestParam("type") String type,
            @RequestParam("startTime") Long startTime,
            @RequestParam("endTime") Long endTime,
            @RequestParam(value = "queryType", required = false, defaultValue = "1") Integer queryType,
            @RequestParam(value = "startRowKey", required = false, defaultValue = "") String startRowKey,
            @RequestParam(value = "size", required = false, defaultValue = "20") Integer size,
            @RequestParam(value = "codes", required = false, defaultValue = "") String codes
    );
}
