package com.weihengtech.iot.eaw.repo.iot.model.dto;

import java.util.List;

public class IotDeltaQueryDto {

    private String deviceSn;
    private Long end;
    private Long start;
    private String times;
    private List<String> metricList;
    private String index;


    public String getDeviceSn() {
        return deviceSn;
    }

    public void setDeviceSn(String deviceSn) {
        this.deviceSn = deviceSn;
    }

    public Long getEnd() {
        return end;
    }

    public void setEnd(Long end) {
        this.end = end;
    }

    public Long getStart() {
        return start;
    }

    public void setStart(Long start) {
        this.start = start;
    }

    public String getTimes() {
        return times;
    }

    public void setTimes(String times) {
        this.times = times;
    }

    public List<String> getMetricList() {
        return metricList;
    }

    public void setMetricList(List<String> metricList) {
        this.metricList = metricList;
    }

    public String getIndex() {
        return index;
    }

    public void setIndex(String index) {
        this.index = index;
    }
}
