package com.weihengtech.iot.eaw.repo.iot.feign.fallback;

import cn.hutool.json.JSONUtil;
import com.weihengtech.iot.eaw.repo.iot.feign.IotAdapterFeign;
import com.weihengtech.iot.eaw.repo.iot.model.dto.*;
import com.weihengtech.pub.infra.resp.model.R;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class IotAdapterFeignFallback implements IotAdapterFeign {

    private final Logger logger = LoggerFactory.getLogger(IotAdapterFeignFallback.class);

    @Override
    public R batchDeviceDetail(String deviceSns) {
        logger.warn("IotAdapterFeignFallback#batchDeviceDetail => {}", deviceSns);
        return R.Companion.fail();
    }

    @Override
    public R batchDeviceProperty(String deviceSns) {
        logger.warn("IotAdapterFeignFallback#batchDevicePropertyBatch => {}", deviceSns);
        return R.Companion.fail();
    }

    @Override
    public R allOnlineDevices() {
        logger.warn("IotAdapterFeignFallback#allOnlineDevices");
        return R.Companion.fail();
    }

    @Override
    public R resetDevice(String deviceSn) {
        logger.warn("IotAdapterFeignFallback#resetDevice => {}", deviceSn);
        return R.Companion.fail();
    }

    @Override
    public R speedup(String deviceSn) {
        logger.warn("IotAdapterFeignFallback#speedup => {}", deviceSn);
        return R.Companion.fail();
    }

    @Override
    public R transportRead(IotTransportReadDto iotTransportReadDto) {
        logger.warn("IotAdapterFeignFallback#transportRead => {}", JSONUtil.toJsonStr(iotTransportReadDto));
        return R.Companion.fail();
    }

    @Override
    public R transportWrite(IotTransportWriteDto iotTransportWriteDto) {
        logger.warn("IotAdapterFeignFallback#transportWrite => {}", JSONUtil.toJsonStr(iotTransportWriteDto));
        return R.Companion.fail();
    }

    @Override
    public R withSampleQuery(IotWithSampleQueryDto iotWithSampleQueryDto) {
        logger.warn("IotAdapterFeignFallback#withSampleQuery => {}", JSONUtil.toJsonStr(iotWithSampleQueryDto));
        return R.Companion.fail();
    }

    @Override
    public R withoutSampleQuery(IotWithoutSampleQueryDto iotWithoutSampleQueryDto) {
        logger.warn("IotAdapterFeignFallback#withoutSampleQuery => {}", JSONUtil.toJsonStr(iotWithoutSampleQueryDto));
        return R.Companion.fail();
    }

    @Override
    public R lastPoint(IotLastPointDto iotLastPointDto) {
        logger.warn("IotAdapterFeignFallback#lastPoint => {}", JSONUtil.toJsonStr(iotLastPointDto));
        return R.Companion.fail();
    }

    @Override
    public R deltaQuery(IotDeltaQueryDto iotDeltaQueryDto) {
        logger.warn("IotAdapterFeignFallback#deltaQuery => {}", JSONUtil.toJsonStr(iotDeltaQueryDto));
        return R.Companion.fail();
    }

    @Override
    public R queryDeviceEvent(String deviceSn, Long start, Long end, Integer page, Integer size, String eventType, String eventName) {
        logger.warn("IotAdapterFeignFallback#queryDeviceEvent => {} {} {} {} {} {} {}", deviceSn, start, end, page, size, eventType, eventName);
        return R.Companion.fail();
    }

    @Override
    public R upgradeOTA(String deviceSn, String otaUrl) {
        logger.warn("IotAdapterFeignFallback#upgradeOTA => {} {}", deviceSn, otaUrl);
        return R.Companion.fail();
    }

    @Override
    public R upgradeOtaV2(String deviceSn, Boolean isGateWay, String otaUrl) {
        logger.warn("IotAdapterFeignFallback#upgradeOTAV2 => {} {} {}", deviceSn, isGateWay, otaUrl);
        return R.Companion.fail();
    }

    @Override
    public R queryOtaStatus(String deviceSn, String taskId) {
        logger.warn("IotAdapterFeignFallback#queryOtaStatus => {} {}", deviceSn, taskId);
        return R.Companion.fail();
    }

    @Override
    public R queryOtaHistory(String deviceSn) {
        logger.warn("IotAdapterFeignFallback#queryOtaHistory => {}", deviceSn);
        return R.Companion.fail();
    }

    @Override
    public R listOtaFirmware(String productName) {
        logger.warn("IotAdapterFeignFallback#listOtaFirmware => {}", productName);
        return R.Companion.fail();
    }

    @Override
    public R batchTransportRead(List<IotBatchTransportReadDto> iotBatchTransportReadDto, String deviceSn) {
        logger.warn("IotAdapterFeignFallback#batchTransportRead => {} {}", deviceSn, JSONUtil.toJsonStr(iotBatchTransportReadDto));
        return R.Companion.fail();
    }

    @Override
    public R batchTransportWrite(List<IotBatchTransportWriteDto> iotBatchTransportWriteDto, String deviceSn) {
        logger.warn("IotAdapterFeignFallback#batchTransportWrite => {} {}", deviceSn, JSONUtil.toJsonStr(iotBatchTransportWriteDto));
        return R.Companion.fail();
    }
}
