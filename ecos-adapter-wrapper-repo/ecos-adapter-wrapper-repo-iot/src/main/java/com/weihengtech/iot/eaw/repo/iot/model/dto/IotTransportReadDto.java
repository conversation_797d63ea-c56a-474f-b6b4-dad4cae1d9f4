package com.weihengtech.iot.eaw.repo.iot.model.dto;

public class IotTransportReadDto {

    private String deviceSn;
    private Integer functionCode;
    private Integer quantity;
    private Integer slaveId;
    private Integer startAddress;


    public String getDeviceSn() {
        return deviceSn;
    }

    public void setDeviceSn(String deviceSn) {
        this.deviceSn = deviceSn;
    }

    public Integer getFunctionCode() {
        return functionCode;
    }

    public void setFunctionCode(Integer functionCode) {
        this.functionCode = functionCode;
    }

    public Integer getQuantity() {
        return quantity;
    }

    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }

    public Integer getSlaveId() {
        return slaveId;
    }

    public void setSlaveId(Integer slaveId) {
        this.slaveId = slaveId;
    }

    public Integer getStartAddress() {
        return startAddress;
    }

    public void setStartAddress(Integer startAddress) {
        this.startAddress = startAddress;
    }

    @Override
    public String toString() {
        return "IotTransportReadDto{" +
                "deviceSn='" + deviceSn + '\'' +
                ", functionCode=" + functionCode +
                ", quantity=" + quantity +
                ", slaveId=" + slaveId +
                ", startAddress=" + startAddress +
                '}';
    }
}
