package com.weihengtech.iot.eaw.repo.iot.feign;

import com.weihengtech.iot.eaw.repo.iot.feign.fallback.IotAdapterFeignFallback;
import com.weihengtech.iot.eaw.repo.iot.model.dto.*;
import com.weihengtech.pub.infra.resp.model.R;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(name = "IotAdapterFeign", url = "${repo.iot.url}", dismiss404 = true, fallback = IotAdapterFeignFallback.class)
public interface IotAdapterFeign {

    @GetMapping("/iotserver/open/ecos/device/detail/batch")
    R batchDeviceDetail(@RequestParam("deviceSns") String deviceSns);

    @GetMapping("/iotserver/open/ecos/device/property/batch")
    R batchDeviceProperty(@RequestParam("deviceSns") String deviceSns);

    @GetMapping("/iotserver/open/ecos/device/online/all")
    R allOnlineDevices();

    @PostMapping("/iotserver/open/ecos/device/reset")
    R resetDevice(@RequestParam("deviceSn") String deviceSn);

    @PostMapping("/iotserver/open/ecos/device/speedup")
    R speedup(@RequestParam("deviceSn") String deviceSn);

    @PostMapping("/iotserver/open/ecos/device/modbus/read")
    R transportRead(@RequestBody IotTransportReadDto iotTransportReadDto);

    @PostMapping("/iotserver/open/ecos/device/modbus/write")
    R transportWrite(@RequestBody IotTransportWriteDto iotTransportWriteDto);

    @PostMapping("/iotserver/open/ecos/device/property/withSampleQuery")
    R withSampleQuery(@RequestBody IotWithSampleQueryDto iotWithSampleQueryDto);

    @PostMapping("/iotserver/open/ecos/device/property/withoutSampleQuery")
    R withoutSampleQuery(@RequestBody IotWithoutSampleQueryDto iotWithoutSampleQueryDto);

    @PostMapping("/iotserver/open/ecos/device/property/lastPoint")
    R lastPoint(@RequestBody IotLastPointDto iotLastPointDto);

    @PostMapping("/iotserver/open/ecos/device/property/deltaQuery")
    R deltaQuery(@RequestBody IotDeltaQueryDto iotDeltaQueryDto);

    @GetMapping("/iotserver/open/ecos/event/page/list")
    R queryDeviceEvent(
            @RequestParam("deviceSn") String deviceSn,
            @RequestParam(value = "start", required = false) Long start,
            @RequestParam(value = "end", required = false) Long end,
            @RequestParam(value = "page", required = false, defaultValue = "1") Integer page,
            @RequestParam(value = "size", required = false, defaultValue = "10") Integer size,
            @RequestParam(value = "eventType", required = false) String eventType,
            @RequestParam(value = "eventName", required = false) String eventName
            );

    @GetMapping("/iotserver/open/ecos/device/ota/update")
    R upgradeOTA(
            @RequestParam("deviceSn") String deviceSn,
            @RequestParam(value = "otaUrl") String otaUrl
    );

    @GetMapping("/iotserver/open/ecos/device/ota/update/V2")
    R upgradeOtaV2(
            @RequestParam("gateWayDeviceSn") String gateWayDeviceSn,
            @RequestParam("isGateWay") Boolean isGateWay,
            @RequestParam(value = "otaUrl") String otaUrl
    );

    @GetMapping("/iotserver/open/ecos/device/ota/update/status")
    R queryOtaStatus(@RequestParam("deviceSn") String deviceSn, @RequestParam("taskId") String taskId);

    @GetMapping("/iotserver/open/ecos/device/ota/update/detail")
    R queryOtaHistory(@RequestParam("deviceSn") String deviceSn);

    @GetMapping("/iotserver/open/ecos/device/ota/update/product")
    R listOtaFirmware(@RequestParam("productName") String productName);

    @PostMapping("/iotserver/open/ecos/device/modbus/read/batch")
    R batchTransportRead(
            @RequestBody List<IotBatchTransportReadDto> iotBatchTransportReadDto,
            @RequestParam("deviceSn") String deviceSn
    );

    @PostMapping("/iotserver/open/ecos/device/modbus/write/batch")
    R batchTransportWrite(
            @RequestBody List<IotBatchTransportWriteDto> iotBatchTransportWriteDto,
            @RequestParam("deviceSn") String deviceSn
    );
}
