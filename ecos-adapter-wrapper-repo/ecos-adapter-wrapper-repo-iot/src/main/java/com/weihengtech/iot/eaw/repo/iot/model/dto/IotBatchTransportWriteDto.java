package com.weihengtech.iot.eaw.repo.iot.model.dto;

import java.util.List;

public class IotBatchTransportWriteDto {
    private Integer byteLength;
    private Integer functionCode;
    private Integer index;
    private Integer slaveId;
    private Integer startAddress;
    private List<Integer> value;

    public Integer getByteLength() {
        return byteLength;
    }

    public void setByteLength(Integer byteLength) {
        this.byteLength = byteLength;
    }

    public Integer getFunctionCode() {
        return functionCode;
    }

    public void setFunctionCode(Integer functionCode) {
        this.functionCode = functionCode;
    }

    public Integer getIndex() {
        return index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }

    public Integer getSlaveId() {
        return slaveId;
    }

    public void setSlaveId(Integer slaveId) {
        this.slaveId = slaveId;
    }

    public Integer getStartAddress() {
        return startAddress;
    }

    public void setStartAddress(Integer startAddress) {
        this.startAddress = startAddress;
    }

    public List<Integer> getValue() {
        return value;
    }

    public void setValue(List<Integer> value) {
        this.value = value;
    }
}
