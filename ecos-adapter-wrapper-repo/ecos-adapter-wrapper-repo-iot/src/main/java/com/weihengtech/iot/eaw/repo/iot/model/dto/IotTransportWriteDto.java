package com.weihengtech.iot.eaw.repo.iot.model.dto;

import java.util.List;

public class IotTransportWriteDto {

    private Integer byteLength;
    private String deviceSn;
    private Integer functionCode;
    private Integer slaveId;
    private Integer startAddress;
    private List<Integer> value;

    public Integer getByteLength() {
        return byteLength;
    }

    public void setByteLength(Integer byteLength) {
        this.byteLength = byteLength;
    }

    public String getDeviceSn() {
        return deviceSn;
    }

    public void setDeviceSn(String deviceSn) {
        this.deviceSn = deviceSn;
    }

    public Integer getFunctionCode() {
        return functionCode;
    }

    public void setFunctionCode(Integer functionCode) {
        this.functionCode = functionCode;
    }

    public Integer getSlaveId() {
        return slaveId;
    }

    public void setSlaveId(Integer slaveId) {
        this.slaveId = slaveId;
    }

    public Integer getStartAddress() {
        return startAddress;
    }

    public void setStartAddress(Integer startAddress) {
        this.startAddress = startAddress;
    }

    public List<Integer> getValue() {
        return value;
    }

    public void setValue(List<Integer> value) {
        this.value = value;
    }

    @Override
    public String toString() {
        return "IotTransportWriteDto{" +
                "byteLength=" + byteLength +
                ", deviceSn='" + deviceSn + '\'' +
                ", functionCode=" + functionCode +
                ", slaveId=" + slaveId +
                ", startAddress=" + startAddress +
                ", value=" + value +
                '}';
    }
}
