package com.weihengtech.iot.eaw.repo.ocpp.model.dto;

public class OcppUpdateFirmwareDto {
    private String location;
    private Integer retries;
    private Long retrieveDate;
    private Integer retryInterval;

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public Integer getRetries() {
        return retries;
    }

    public void setRetries(Integer retries) {
        this.retries = retries;
    }

    public Long getRetrieveDate() {
        return retrieveDate;
    }

    public void setRetrieveDate(Long retrieveDate) {
        this.retrieveDate = retrieveDate;
    }

    public Integer getRetryInterval() {
        return retryInterval;
    }

    public void setRetryInterval(Integer retryInterval) {
        this.retryInterval = retryInterval;
    }
}
