package com.weihengtech.iot.eaw.repo.ocpp.feign;

import com.weihengtech.iot.eaw.repo.ocpp.feign.fallback.OcppAdapterFeignFallback;
import com.weihengtech.iot.eaw.repo.ocpp.model.dto.*;
import com.weihengtech.pub.infra.resp.model.R;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = "OcppAdapterFeign", url = "${repo.ocpp.url}", dismiss404 = true, fallback = OcppAdapterFeignFallback.class)
public interface OcppAdapterFeign {
    @PostMapping("/v1/cp/reset")
    R cpReset(@RequestParam("id") String id, @RequestBody OcppCpResetDto ocppCpResetDto);

    @PostMapping("/v1/cp/charge/default_profile")
    R cpDefaultProfile(@RequestParam("id") String id, @RequestBody OcppCpDefaultProfileDto ocppCpDefaultProfileDto);

    @PostMapping("/v1/cp/charge/max_profile")
    R cpMaxProfile(@RequestParam("id") String id, @RequestBody OcppCpMaxProfileDto ocppCpMaxProfileDto);

    @GetMapping("/v1/cp/charge/start")
    R cpChargeStart(@RequestParam("id") String id, @RequestParam("transactionId") Integer transactionId);

    @GetMapping("/v1/cp/charge/stop")
    R cpChargeStop(@RequestParam("id") String id, @RequestParam("transactionId") Integer transactionId);

    @GetMapping("/v1/cp/status")
    R cpStatus(@RequestParam("id") String id);

    @GetMapping("/v1/cp/info")
    R cpInfo(@RequestParam("id") String id);

    @GetMapping("/v1/cp/online")
    R cpOnline();

    @GetMapping("/v1/cp/is_online")
    R cpIsOnline(@RequestParam("id") String id);

    @GetMapping("/v1/cp/firmware/status")
    R cpFirmwareStatus(@RequestParam("id") String id);

    @PostMapping("/v1/cp/firmware/update")
    R cpFirmwareUpdate(@RequestParam("id") String id, @RequestBody OcppUpdateFirmwareDto ocppUpdateFirmwareDto);

    @PostMapping("/v1/cp/data_transfer")
    R cpDataTransfer(@RequestParam("id") String id, @RequestBody OcppDataTransferDto ocppDataTransferDto);

    @PostMapping("/v1/cp/diagnostics")
    R cpDiagnostics(@RequestParam("id") String id, @RequestBody OcppDiagnosticsDto ocppDiagnosticsDto);

    @PostMapping("/v1/card/add")
    R cardAdd(@RequestParam("id") String id, @RequestBody OcppCardAddDto cardAddDto);

    @PostMapping("/v1/card/remove")
    R cardRemove(@RequestParam("id") String id, @RequestBody OcppCardRemoveDto cardRemoveDto);

    @PostMapping("/v1/card/cover")
    R cardCover(@RequestParam("id") String id, @RequestBody OcppCardCoverDto ocppCardCoverDto);

    @GetMapping("/v1/card/list")
    R cardList(@RequestParam("id") String id);

    @GetMapping("/v1/cp/charge/configuration")
    R getConfiguration(@RequestParam("id") String id, @RequestParam("keys") String keys);

    @PutMapping("/v1/cp/charge/configuration")
    R updConfiguration(@RequestParam("id") String id, @RequestBody OcppConfigurationDTO ocppConfigurationDTO);
}
