package com.weihengtech.iot.eaw.repo.ocpp.feign.fallback;

import cn.hutool.json.JSON;
import cn.hutool.json.JSONUtil;
import com.weihengtech.iot.eaw.repo.ocpp.feign.OcppAdapterFeign;
import com.weihengtech.iot.eaw.repo.ocpp.model.dto.*;
import com.weihengtech.pub.infra.resp.model.R;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
public class OcppAdapterFeignFallback implements OcppAdapterFeign {

    private final Logger logger = LoggerFactory.getLogger(OcppAdapterFeignFallback.class);

    @Override
    public R cpReset(String id, OcppCpResetDto ocppCpResetDto) {
        logger.warn("cpReset [{}] {}", id, JSONUtil.toJsonStr(ocppCpResetDto));
        return R.Companion.fail();
    }

    @Override
    public R cpDefaultProfile(String id, OcppCpDefaultProfileDto ocppCpDefaultProfileDto) {
        logger.warn("cpDefaultProfile [{}] {}", id, JSONUtil.toJsonStr(ocppCpDefaultProfileDto));
        return R.Companion.fail();
    }

    @Override
    public R cpMaxProfile(String id, OcppCpMaxProfileDto ocppCpMaxProfileDto) {
        logger.warn("cpMaxProfile [{}] {}", id, JSONUtil.toJsonStr(ocppCpMaxProfileDto));
        return R.Companion.fail();
    }

    @Override
    public R cpChargeStart(String id, Integer transactionId) {
        logger.warn("cpChargeStart [{}] {}", id, transactionId);
        return R.Companion.fail();
    }

    @Override
    public R cpChargeStop(String id, Integer transactionId) {
        logger.warn("cpChargeStop [{}] {}", id, transactionId);
        return R.Companion.fail();
    }

    @Override
    public R cpStatus(String id) {
        logger.warn("cpStatus [{}]", id);
        return R.Companion.fail();
    }

    @Override
    public R cpInfo(String id) {
        logger.warn("cpInfo [{}]", id);
        return R.Companion.fail();
    }

    @Override
    public R cpOnline() {
        logger.warn("cpOnline");
        return R.Companion.fail();
    }

    @Override
    public R cpIsOnline(String id) {
        logger.warn("cpIsOnline [{}]", id);
        return R.Companion.fail();
    }

    @Override
    public R cpFirmwareStatus(String id) {
        logger.warn("cpFirmwareStatus [{}]", id);
        return null;
    }

    @Override
    public R cpFirmwareUpdate(String id, OcppUpdateFirmwareDto ocppUpdateFirmwareDto) {
        logger.warn("cpFirmwareUpdate [{}] {}", id, JSONUtil.toJsonStr(ocppUpdateFirmwareDto));
        return null;
    }

    @Override
    public R cpDataTransfer(String id, OcppDataTransferDto ocppDataTransferDto) {
        logger.warn("cpDataTransfer [{}] {}", id, JSONUtil.toJsonStr(ocppDataTransferDto));
        return null;
    }

    @Override
    public R cpDiagnostics(String id, OcppDiagnosticsDto ocppDiagnosticsDto) {
        logger.warn("cpDiagnostics [{}] {}", id, JSONUtil.toJsonStr(ocppDiagnosticsDto));
        return null;
    }

    @Override
    public R cardAdd(String id, OcppCardAddDto cardAddDto) {
        logger.warn("cardAdd [{}] {}", id, JSONUtil.toJsonStr(cardAddDto));
        return R.Companion.fail();
    }

    @Override
    public R cardRemove(String id, OcppCardRemoveDto cardRemoveDto) {
        logger.warn("cardRemove [{}] {}", id, JSONUtil.toJsonStr(cardRemoveDto));
        return R.Companion.fail();
    }

    @Override
    public R cardCover(String id, OcppCardCoverDto ocppCardCoverDto) {
        logger.warn("cardCover [{}] {}", id, JSONUtil.toJsonStr(ocppCardCoverDto));
        return R.Companion.fail();
    }

    @Override
    public R cardList(String id) {
        logger.warn("cardList [{}]", id);
        return R.Companion.fail();
    }

    @Override
    public R getConfiguration(String id, String keys) {
        logger.warn("getConfiguration [{}, {}]", id, keys);
        return R.Companion.fail();
    }

    @Override
    public R updConfiguration(String id, OcppConfigurationDTO ocppConfigurationDTO) {
        logger.warn("updConfiguration [{}, {}]", id, JSONUtil.toJsonStr(ocppConfigurationDTO));
        return R.Companion.fail();
    }
}
