package com.weihengtech.iot.eaw.repo.ocpp.model.dto;

import java.util.List;

public class OcppCpDefaultProfileDto {

    private ChargingSchedule chargingSchedule;

    public ChargingSchedule getChargingSchedule() {
        return chargingSchedule;
    }

    public void setChargingSchedule(ChargingSchedule chargingSchedule) {
        this.chargingSchedule = chargingSchedule;
    }

    public static class ChargingSchedule {
        private List<ChargingSchedulePeriod> chargingSchedulePeriod;

        private String chargingRateUnit;

        public List<ChargingSchedulePeriod> getChargingSchedulePeriod() {
            return chargingSchedulePeriod;
        }

        public void setChargingSchedulePeriod(List<ChargingSchedulePeriod> chargingSchedulePeriod) {
            this.chargingSchedulePeriod = chargingSchedulePeriod;
        }

        public String getChargingRateUnit() {
            return chargingRateUnit;
        }

        public void setChargingRateUnit(String chargingRateUnit) {
            this.chargingRateUnit = chargingRateUnit;
        }
    }

    public static class ChargingSchedulePeriod {
        private Integer startPeriod;
        private Long limit;
        private Integer numberPhases;

        public Integer getStartPeriod() {
            return startPeriod;
        }

        public void setStartPeriod(Integer startPeriod) {
            this.startPeriod = startPeriod;
        }

        public Long getLimit() {
            return limit;
        }

        public void setLimit(Long limit) {
            this.limit = limit;
        }

        public Integer getNumberPhases() {
            return numberPhases;
        }

        public void setNumberPhases(Integer numberPhases) {
            this.numberPhases = numberPhases;
        }
    }
}
