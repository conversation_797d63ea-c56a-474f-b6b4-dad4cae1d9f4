infra.doc.enable=true
infra.doc.title=ECOS-IOT-DOCUMENT
infra.doc.version=1.0.0
infra.doc.description=ecos iot aggregate device api from different Platform
infra.doc.contact-name=jiajun.li
infra.doc.contact-email=<EMAIL>
infra.doc.groups.v1.scan=com.weihengtech.iot.eaw.web.v1.controller
infra.doc.groups.v1.path=/v1/**

repo.iot.url=${REPO_IOT_URL:http://iot-server-2.dev.weiheng-tech.com}
repo.ocpp.url=${REPO_OCPP_URL:https://ocpp-cs.weiheng-tech.com}
repo.tuya.url=${REPO_TUYA_URL:http://ecos-tuya-service.ecos.svc.cluster.local:20300}
repo.elink.url=${REPO_ELINK_URL:http://aeb-flower.ecos-elinter-bridge.svc.cluster.local:18002}
repo.elink.access-key=
repo.elink.access-secret=
repo.elink.endpoint=iot.cn-shanghai.aliyuncs.com
repo.elink.instance-id=iot-060a5wsr
repo.elink.product-key=a1CcUmvwswd
repo.elink.region-id=cn-shanghai
