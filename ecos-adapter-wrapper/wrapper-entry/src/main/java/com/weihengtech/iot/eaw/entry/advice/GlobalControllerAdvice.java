package com.weihengtech.iot.eaw.entry.advice;

import com.weihengtech.iot.eaw.aggregation.api.exception.AggregationRequestFailureException;
import com.weihengtech.iot.eaw.infra.exception.ServiceFallbackException;
import com.weihengtech.iot.eaw.service.api.exception.ServiceCommandNotSupportedException;
import com.weihengtech.iot.eaw.service.api.exception.ServiceHitException;
import com.weihengtech.pub.infra.resp.model.R;
import org.springframework.http.HttpStatus;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.util.HashMap;
import java.util.List;

@RestControllerAdvice(basePackages = "com.weihengtech.iot.eaw.web")
public class GlobalControllerAdvice {

    @ExceptionHandler(ServiceCommandNotSupportedException.class)
    public R handleServiceCommandNotSupportedException(ServiceCommandNotSupportedException ignored) {
        return R.Companion.fail(HttpStatus.BAD_REQUEST.value(), HttpStatus.BAD_REQUEST.getReasonPhrase(), "Illegal code");
    }

    @ExceptionHandler(AggregationRequestFailureException.class)
    public R handleAggregationRequestFailureException(AggregationRequestFailureException ignored) {
        return R.Companion.fail(HttpStatus.BAD_GATEWAY.value(), HttpStatus.BAD_GATEWAY.getReasonPhrase());
    }

    @ExceptionHandler(ServiceHitException.class)
    public R handleSourceTypeHitException(ServiceHitException ignored) {
        return R.Companion.fail(HttpStatus.BAD_REQUEST.value(), HttpStatus.BAD_REQUEST.getReasonPhrase(), "Illegal platform or category or pid");
    }

    @ExceptionHandler(ServiceFallbackException.class)
    public R handleSourceTypeHitException(ServiceFallbackException ignored) {
        return R.Companion.fail(HttpStatus.BAD_REQUEST.value(), HttpStatus.BAD_REQUEST.getReasonPhrase(), "Request Limit");
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    public R handleMethodArgumentNotValidException(MethodArgumentNotValidException e) {
        List<FieldError> fieldErrors = e.getFieldErrors();
        if (CollectionUtils.isEmpty(fieldErrors)) {
            return R.Companion.fail(HttpStatus.BAD_REQUEST.value(), HttpStatus.BAD_REQUEST.getReasonPhrase());
        }
        FieldError fieldError = fieldErrors.get(0);
        HashMap<String, Object> map = new HashMap<>(1);
        map.put(fieldError.getField(), fieldError.getDefaultMessage());
        return R.Companion.fail(HttpStatus.BAD_REQUEST.value(), HttpStatus.BAD_REQUEST.getReasonPhrase(), map);
    }
}
