import org.springframework.boot.gradle.tasks.bundling.BootJar

dependencies {
    implementation(project(":wrapper-web-v1"))
    implementation(project(":wrapper-infra"))
}

tasks.withType(BootJar::class.java) {
    enabled = true
}

plugins {
    id("com.bmuschko.docker-remote-api") version "6.7.0"
    id("com.bmuschko.docker-spring-boot-application") version "6.7.0"
}

apply {
    plugin("com.bmuschko.docker-remote-api")
    plugin("com.bmuschko.docker-spring-boot-application")
}

docker {
    url = project.ext.properties["docker_url"].toString()
    springBootApplication {
        baseImage.set("harbor.weiheng-tech.com/library/eclipse-temurin:17-jdk-alpine")
        maintainer.set(project.ext.properties["docker_maintainer"].toString())
        ports.set(listOf(8080))
        mainClassName.set("com.weihengtech.iot.eaw.entry.EcosAdapterWrapperApplication")
        images.set(listOf("harbor.weiheng-tech.com/${project.ext.properties["harbor_env"].toString()}/iot/ecos-adapter-wrapper:${project.ext.properties["image_version"].toString()}"))
    }
    registryCredentials {
        url.set("harbor.weiheng-tech.com/${project.ext.properties["harbor_env"].toString()}/iot/ecos-adapter-wrapper")
        username.set(project.ext.properties["harbor_username"].toString())
        password.set(project.ext.properties["harbor_password"].toString())
    }
}

tasks.register("processDocker") {
    dependsOn("dockerBuildImage", "dockerPushImage")
}