package com.weihengtech.iot.eaw.infra.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.ThreadPoolExecutor;

/**
 * <AUTHOR>
 * @date 2025/8/18 15:59
 * @version 1.0
 */
@Configuration
public class ThreadPoolConfig {

    @Bean
    @Primary
    public ThreadPoolTaskExecutor threadPoolTaskExecutor() {
        // 使用visibleThreadPoolTaskExecutor
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        // 通过Runtime方法来获取当前服务器cpu内核，根据cpu内核来创建核心线程数和最大线程数
        // int availableProcessors = Runtime.getRuntime().availableProcessors();
        /*
        * 配置线程个数
        如果是CPU密集型任务，那么线程池的线程个数应该尽量少一些，一般为CPU的个数+1条线程(大量计算)
        如果是IO密集型任务，那么线程池的线程可以放的很大，如2*CPU的个数(IO操作)
        */
        executor.setCorePoolSize(2);
        // 允许线程池超时
        executor.setAllowCoreThreadTimeOut(false);
        // 配置最大线程数
        executor.setMaxPoolSize(4);
        // 空闲存活时间
        executor.setKeepAliveSeconds(60);
        // 设置 等待终止秒数
        executor.setAwaitTerminationSeconds(60);
        // 配置队列大小
        executor.setQueueCapacity(60);
        // 配置线程池中的线程的名称前缀
        executor.setThreadNamePrefix("iot-async-processor-");

        // rejection-policy：当pool已经达到max size的时候，丢弃最老任务
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.DiscardOldestPolicy());
        return executor;
    }
}
