package com.weihengtech.iot.eaw.web.v1.controller;

import cn.hutool.core.util.ReflectUtil;
import cn.hutool.json.JSONUtil;
import com.weihengtech.iot.eaw.infra.exception.ServiceFallbackException;
import com.weihengtech.iot.eaw.service.api.exception.ServiceHitException;
import com.weihengtech.iot.eaw.service.v1.consts.ServiceNames;
import com.weihengtech.iot.eaw.web.v1.model.dto.CommandPublishDTO;
import com.weihengtech.pub.infra.resp.model.R;
import com.weihengtech.pub.infra.utils.annotation.ParameterLimit;
import com.weihengtech.pub.infra.utils.annotation.ParameterResolver;
import com.weihengtech.pub.infra.utils.consts.ParameterType;
import com.weihengtech.pub.infra.utils.holder.ParameterResolverHolder;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/v1")
@Tag(name = "通用命令", description = "通用命令接口封装")
public class CommandController {

    private static final org.slf4j.Logger log = LoggerFactory.getLogger(CommandController.class);

    @Resource
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    @GetMapping("/{platform}/{category}/commands")
    @Operation(summary = "查询支持的命令")
    @Parameters(value = {
            @Parameter(name = "platform", description = "平台", required = true, in = ParameterIn.PATH),
            @Parameter(name = "category", description = "品类", required = true, in = ParameterIn.PATH),
            @Parameter(name = "p", description = "产品标识", in = ParameterIn.QUERY)
    })
    @ParameterResolver(names = {"platform", "category", "pId"}, joinName = ServiceNames.JOIN_NAME, exception = ServiceHitException.class)
    public R querySupportedCommands(@PathVariable("platform") String platform, @PathVariable("category") String category,
                                    @RequestParam(name = "p", required = false, defaultValue = "") String pId) {
        return R.Companion.success(ReflectUtil.invoke(ParameterResolverHolder.Companion.get(), "querySupportedCommands"));
    }

    @PostMapping("/{platform}/{category}/commands")
    @Operation(summary = "发送命令")
    @Parameters(value = {
            @Parameter(name = "platform", description = "平台", required = true, in = ParameterIn.PATH),
            @Parameter(name = "category", description = "品类", required = true, in = ParameterIn.PATH),
            @Parameter(name = "d", description = "设备标识", in = ParameterIn.QUERY),
            @Parameter(name = "p", description = "产品标识", in = ParameterIn.QUERY),
    })
    @ParameterResolver(names = {"platform", "category", "p"}, joinName = ServiceNames.JOIN_NAME, exception = ServiceHitException.class)
    @ParameterLimit(
            key = "d",
            paramType = ParameterType.BODY,
            body = CommandPublishDTO.class,
            hits = {"upgrade"},
            exclude = {"is_online"},
            recover = 5 * 60,
            exception = ServiceFallbackException.class
    )
    public R sendCommand(@PathVariable("platform") String platform,@PathVariable("category") String category,
                         @RequestParam(name = "p", required = false, defaultValue = "") String p,
                         @RequestParam(name = "d", required = false, defaultValue = "") String d,
                         @RequestBody @Valid CommandPublishDTO commandPublishDTO
    ) {
        return R.Companion.success(
                ReflectUtil.invoke(ParameterResolverHolder.Companion.get(),
                "sendCommand",
                        d,
                        commandPublishDTO.getCode(),
                        commandPublishDTO.getValue())
        );
    }

    @PostMapping("/{platform}/{category}/commands/async")
    @Operation(summary = "发送命令(异步，及时响应)")
    @Parameters(value = {
            @Parameter(name = "platform", description = "平台", required = true, in = ParameterIn.PATH),
            @Parameter(name = "category", description = "品类", required = true, in = ParameterIn.PATH),
            @Parameter(name = "d", description = "设备标识", in = ParameterIn.QUERY),
            @Parameter(name = "p", description = "产品标识", in = ParameterIn.QUERY),
    })
    @ParameterResolver(names = {"platform", "category", "p"}, joinName = ServiceNames.JOIN_NAME, exception = ServiceHitException.class)
    @ParameterLimit(
            key = "d",
            paramType = ParameterType.BODY,
            body = CommandPublishDTO.class,
            hits = {"upgrade"},
            exclude = {"is_online"},
            recover = 5 * 60,
            exception = ServiceFallbackException.class
    )
    public R sendCommandAsync(@PathVariable("platform") String platform,@PathVariable("category") String category,
                         @RequestParam(name = "p", required = false, defaultValue = "") String p,
                         @RequestParam(name = "d", required = false, defaultValue = "") String d,
                         @RequestBody @Valid CommandPublishDTO commandPublishDTO
    ) {
        Object obj = ParameterResolverHolder.Companion.get();
        threadPoolTaskExecutor.execute(() -> {
            Object res = ReflectUtil.invoke(obj,
                    "sendCommand",
                    d,
                    commandPublishDTO.getCode(),
                    commandPublishDTO.getValue());
            log.info("send command device is :{}, param is: {}, result: {}", d, JSONUtil.toJsonStr(commandPublishDTO),
                    res instanceof Boolean ? res : JSONUtil.toJsonStr(res));
        });
        return R.Companion.success(Boolean.TRUE);
    }
}
