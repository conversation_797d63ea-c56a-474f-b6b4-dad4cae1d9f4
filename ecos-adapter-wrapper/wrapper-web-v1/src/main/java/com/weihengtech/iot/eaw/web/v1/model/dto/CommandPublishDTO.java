package com.weihengtech.iot.eaw.web.v1.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;

public class CommandPublishDTO {

    @Schema(description = "命令标识", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty
    private String code;

    @Schema(description = "命令主体", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull
    private Object value;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public Object getValue() {
        return value;
    }

    public void setValue(Object value) {
        this.value = value;
    }

    @Override
    public String toString() {
        return "CommandPublishDTO{" +
                "code='" + code + '\'' +
                ", value=" + value +
                '}';
    }
}
