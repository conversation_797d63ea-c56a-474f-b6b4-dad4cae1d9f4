package com.weihengtech.iot.eaw.web.v1.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 心跳检测api
 *
 * <AUTHOR>
 * @date 2025/1/20 13:57
 * @version 1.0
 */
@RestController
@RequestMapping("/common")
@Tag(name = "心跳检测接口", description = "心跳检测接口")
public class HeartBeatController {

    @GetMapping("/health")
    public String health() {
        return "heart beat";
    }
}
