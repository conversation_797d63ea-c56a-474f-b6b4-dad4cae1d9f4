---
--- Generated by <PERSON><PERSON><PERSON>(https://github.com/<PERSON><PERSON><PERSON>)
--- Created by jiajun.li.
--- DateTime: 2024/2/22 14:55
---
local os = require("os")
-- local version = "17.0.3.3"
-- local profile = "dev"
--local profile = "product"
local profile = arg[1]
local version = arg[2]

os.execute("gradle clean bootJar --no-daemon")
os.execute("docker build -t harbor.weiheng-tech.com/"..profile.."/iot/ecos-adapter-wrapper:"..version.." .")
os.execute("docker push harbor.weiheng-tech.com/"..profile.."/iot/ecos-adapter-wrapper:"..version)