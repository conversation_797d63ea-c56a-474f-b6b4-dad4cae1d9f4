plugins {
    java
    `java-library`
    id("org.springframework.boot") version "3.1.3"
    id("io.spring.dependency-management") version "1.1.3"
    kotlin("jvm") version "1.8.22"
    kotlin("plugin.spring") version "1.8.22"
}

extra["springCloudVersion"] = "2022.0.4"
var path: String = rootProject.projectDir.absolutePath
var config: String = "${File(path).parent}${File.separator}config.gradle"
var auth: String = "${File(path).parent}${File.separator}auth.gradle"

group = "com.weihengtech.iot"
version = "17.0.1.9"


allprojects {
    apply {
        plugin("org.springframework.boot")
        plugin("io.spring.dependency-management")
        plugin("java")
        plugin("java-library")
        plugin("kotlin")
        from(config)
        from(auth)
    }

    java {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
        toolchain {
            languageVersion.set(JavaLanguageVersion.of(17))
        }
    }

    repositories {
        maven { url = uri("https://maven.aliyun.com/repository/public/") }
        maven { url = uri("https://oss.sonatype.org/content/repositories/snapshots/") }
        maven {
            credentials {
                username = rootProject.ext.properties["username"].toString()
                password = rootProject.ext.properties["password"].toString()
            }
            isAllowInsecureProtocol = true
            url = uri("http://nexus.dev.weiheng-tech.com/repository/maven-snapshots/")
        }
        maven {
            credentials {
                username = rootProject.ext.properties["username"].toString()
                password = rootProject.ext.properties["password"].toString()
            }
            isAllowInsecureProtocol = true
            url = uri("http://nexus.dev.weiheng-tech.com/repository/maven-releases/")
        }
        mavenCentral()
    }

    dependencyManagement {
        imports {
            mavenBom("org.springframework.cloud:spring-cloud-dependencies:${property("springCloudVersion")}")
            mavenBom("com.weihengtech.lib:lib-kt-dependencies:${rootProject.ext.properties["dependencies_version"].toString()}")
        }
        dependencies {
            dependency("com.weihengtech.iot:ecos-adapter-wrapper-service-api:17.0.2.0-20240909.103903-1")
            dependency("com.weihengtech.iot:ecos-adapter-wrapper-service-impl-v1:17.0.2.0-20250902.055759-21")
            dependency("com.weihengtech.iot:ecos-adapter-wrapper-aggregation-api:17.0.1.0-20250902.033531-14")
            dependency("cn.hutool:hutool-all:5.8.25")
        }
    }

    dependencies {
        implementation("com.weihengtech.iot:ecos-adapter-wrapper-service-api")
        implementation("com.weihengtech.iot:ecos-adapter-wrapper-service-impl-v1")
        implementation("com.weihengtech.iot:ecos-adapter-wrapper-aggregation-api")

        implementation("com.weihengtech.lib:log-kt-spring-boot-starter")
        implementation("com.weihengtech.lib:doc-kt-spring-boot-starter")
        implementation("com.weihengtech.lib:response-kt-spring-boot-starter")
        implementation("com.weihengtech.lib:utils-kt-spring-boot-starter")
        implementation("com.weihengtech.lib:security-kt-spring-boot-starter")

        implementation("org.springframework.boot:spring-boot-starter-web")
        implementation("org.springframework.boot:spring-boot-starter-validation")
        implementation("org.springframework.boot:spring-boot-starter-actuator")
        implementation("org.springframework.cloud:spring-cloud-starter-openfeign")
        implementation("org.springframework.cloud:spring-cloud-starter-circuitbreaker-resilience4j")

        implementation("cn.hutool:hutool-all")

        annotationProcessor("org.springframework.boot:spring-boot-configuration-processor")
    }

    configurations {
        implementation {
            exclude(group = "org.springframework.boot", module = "spring-boot-starter-logging")
            exclude(group = "org.yaml", module = "snakeyaml")
        }
    }

    tasks.register("processBootJar") {
        dependsOn("clean", "build", "bootJar")
    }
}

springBoot {
    mainClass.set("com.weihengtech.iot.eaw.entry.EcosAdapterWrapperApplication")
}