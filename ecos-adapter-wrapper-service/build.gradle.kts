import org.springframework.boot.gradle.tasks.bundling.BootJar

plugins {
    java
    `java-library`
    id("maven-publish")
    id("org.springframework.boot") version "3.1.3"
    id("io.spring.dependency-management") version "1.1.3"
    kotlin("jvm") version "1.8.22"
    kotlin("plugin.spring") version "1.8.22"
}

extra["springCloudVersion"] = "2022.0.4"
var path: String = rootProject.projectDir.absolutePath
var config: String = "${File(path).parent}${File.separator}config.gradle"
var auth: String = "${File(path).parent}${File.separator}auth.gradle"

allprojects {
    apply {
        plugin("org.springframework.boot")
        plugin("io.spring.dependency-management")
        plugin("java")
        plugin("java-library")
        plugin("maven-publish")
        plugin("kotlin")
        from(config)
        from(auth)
    }
    group = rootProject.ext.properties["group"].toString()
    version = rootProject.ext.properties[project.name.replace("-","_")].toString()
    java {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }

    repositories {
        maven { url = uri("https://maven.aliyun.com/repository/public/") }
        maven { url = uri("https://oss.sonatype.org/content/repositories/snapshots/") }
        maven {
            credentials {
                username = rootProject.ext.properties["username"].toString()
                password = rootProject.ext.properties["password"].toString()
            }
            isAllowInsecureProtocol = true
            url = uri("http://nexus.dev.weiheng-tech.com/repository/maven-snapshots/")
        }
        maven {
            credentials {
                username = rootProject.ext.properties["username"].toString()
                password = rootProject.ext.properties["password"].toString()
            }
            isAllowInsecureProtocol = true
            url = uri("http://nexus.dev.weiheng-tech.com/repository/maven-releases/")
        }
        mavenCentral()
    }

    dependencyManagement {
        imports {
            mavenBom("org.springframework.cloud:spring-cloud-dependencies:${property("springCloudVersion")}")
            mavenBom("com.weihengtech.lib:lib-kt-dependencies:${rootProject.ext.properties["dependencies_version"].toString()}")
        }
    }

    dependencies {
        implementation("org.springframework.boot:spring-boot-starter-web")
        implementation("cn.hutool:hutool-all:5.8.25")
        annotationProcessor("org.springframework.boot:spring-boot-configuration-processor")
    }

    configurations {
        implementation {
            exclude(group = "org.springframework.boot", module = "spring-boot-starter-logging")
            exclude(group = "org.yaml", module = "snakeyaml")
        }
    }

    tasks.withType(BootJar::class.java) {
        enabled = false
    }

    tasks.register("processPublish") {
        dependsOn("clean", "build", "publish")
    }
}

