[{"code": "switch_button", "type": "Boolean", "description": "开关1", "value": true}, {"code": "switch_button2", "type": "Boolean", "description": "开关2", "value": true}, {"code": "countdown", "type": "Integer", "description": "开关1倒计时, 单位秒", "value": 10}, {"code": "countdown2", "type": "Integer", "description": "开关2倒计时, 单位秒", "value": 10}, {"code": "relay_status", "type": "String", "description": "上电状态, 枚举last, power_on, power_off", "value": "memory"}, {"code": "child_lock", "type": "Boolean", "description": "童锁开关", "value": true}, {"code": "light_mode", "type": "String", "description": "指示灯状态: relay, pos, none", "value": "relay"}, {"code": "overcharge_switch", "type": "Boolean", "description": "过充保护开关", "value": true}, {"code": "cycle_time", "type": "Array", "description": "循环定时, 最多10个, [开启时长+结束时长]必须小于[结束时间-开始时间]", "value": [{"switch_button": true, "week": 20, "start_minute": 28260528, "end_minute": 28260628, "open_duration": 10, "close_duration": 10}]}, {"code": "random_time", "type": "Array", "description": "随机定时, 最多16个, 00单次, 01周日, 02周一, 04周二, 08周三, 16周四, 32周五, 64周六, 多选相加", "value": [{"switch_button": true, "week": 20, "start_minute": 28260528, "end_minute": 28260628}]}]