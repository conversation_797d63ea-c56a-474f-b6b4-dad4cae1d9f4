[{"code": "charge_point_reset", "type": "String", "description": "重置充电桩 Hard 或者 Soft", "value": "Soft"}, {"code": "start_charging", "type": "Integer", "description": "开始充电", "value": "1"}, {"code": "stop_charging", "type": "Integer", "description": "停止充电", "value": "1"}, {"code": "charge_point_status", "type": "None", "description": "充电桩状态", "value": "<PERSON>l"}, {"code": "charge_point_info", "type": "None", "description": "充电桩信息", "value": "<PERSON>l"}, {"code": "add_card", "type": "None", "description": "新增卡片", "value": "<PERSON>l"}, {"code": "remove_cards", "type": "None", "description": "移除卡片", "value": ["SN1234"]}, {"code": "cover_cards", "type": "Array", "description": "覆盖卡片", "value": ["SN1234"]}, {"code": "query_cards_list", "type": "None", "description": "查看绑定卡片", "value": "<PERSON>l"}, {"code": "charge_point_default_profile", "type": "Object", "description": "充电桩默认配置文件", "value": {"chargeSchedulePeriods": [{"limit": 5, "numberPhases": 3}], "chargingRateUnit": "W"}}, {"code": "charge_point_max_profile", "type": "Object", "description": "充电桩阈值配置文件", "value": {"limit": 5, "unit": "W", "numberPhases": 3}}, {"code": "charge_point_firmware_status", "type": "None", "description": "充电桩固件状态", "value": "<PERSON>l"}, {"code": "charge_point_firmware_update", "type": "Object", "description": "充电桩固件升级", "value": {"location": "固件升级地址", "retries": 3, "retry_interval": 10, "retrieve_date": 0}}, {"code": "charge_point_shutdown_load_balance", "type": "None", "description": "充电桩关闭负载均衡", "value": "<PERSON>l"}, {"code": "charge_point_data_transfer", "type": "String", "description": "充电桩透传指令", "value": "0142040A010000"}, {"code": "charge_point_get_diagnostics", "type": "Object", "description": "充电桩获取诊断日志", "value": {"start_time": 0, "end_time": 0}}, {"code": "get_configuration", "type": "String", "description": "获取充电桩全局配置", "value": "ChargeProfileMaxStackLevel"}, {"code": "upd_configuration", "type": "Object", "description": "更新充电桩全局配置", "value": {"key": "ChargeProfileMaxStackLevel", "value": "true"}}]