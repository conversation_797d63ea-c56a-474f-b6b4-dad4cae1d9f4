[{"code": "switch_button", "type": "Object", "description": "开关", "value": {"index": 0, "value": true}}, {"code": "countdown", "type": "Object", "description": "倒计时, 单位秒", "value": {"index": 0, "value": 10}}, {"code": "relay_status", "type": "String", "description": "上电状态, 枚举last, power_on, power_off", "value": "last"}, {"code": "child_lock", "type": "Boolean", "description": "童锁开关", "value": true}, {"code": "light_mode", "type": "String", "description": "指示灯状态: relay, pos, none", "value": "relay"}, {"code": "overcharge_switch", "type": "Boolean", "description": "过充保护开关", "value": true}, {"code": "cycle_time", "type": "Array", "description": "循环定时, 最多10个, [开启时长+结束时长]必须小于[结束时间-开始时间]", "value": ""}, {"code": "random_time", "type": "Array", "description": "随机定时, 最多16个, 00单次, 01周日, 02周一, 04周二, 08周三, 16周四, 32周五, 64周六, 多选相加", "value": ""}]