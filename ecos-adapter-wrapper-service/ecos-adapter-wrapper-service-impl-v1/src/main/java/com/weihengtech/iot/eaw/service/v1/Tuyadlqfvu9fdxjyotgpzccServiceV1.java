package com.weihengtech.iot.eaw.service.v1;

import cn.hutool.core.util.ReflectUtil;
import com.weihengtech.iot.eaw.aggregation.tuya.Tuyadlqfvu9fdxjyotgpzccAgg;
import com.weihengtech.iot.eaw.service.api.AbstractCommandService;
import com.weihengtech.iot.eaw.service.api.consts.CategoryTypes;
import com.weihengtech.iot.eaw.service.api.consts.PlatformTypes;
import com.weihengtech.iot.eaw.service.api.model.bo.DeviceControlCommandBo;
import com.weihengtech.iot.eaw.service.v1.consts.ServiceNames;
import com.weihengtech.iot.eaw.service.v1.util.CommandUtil;
import com.weihengtech.iot.eaw.service.v1.util.JsonCommandLoader;
import org.springframework.stereotype.Service;

import java.util.List;

@Service(PlatformTypes.tuya + CategoryTypes.DLQ + "fvu9fdxjyotgpzcc" + ServiceNames.JOIN_NAME)
public class Tuyadlqfvu9fdxjyotgpzccServiceV1 extends AbstractCommandService {

    private final Tuyadlqfvu9fdxjyotgpzccAgg tuyadlqfvu9fdxjyotgpzccAgg;
    private final JsonCommandLoader jsonCommandLoader;

    public Tuyadlqfvu9fdxjyotgpzccServiceV1(Tuyadlqfvu9fdxjyotgpzccAgg tuyadlqfvu9fdxjyotgpzccAgg, JsonCommandLoader jsonCommandLoader) {
        this.tuyadlqfvu9fdxjyotgpzccAgg = tuyadlqfvu9fdxjyotgpzccAgg;
        this.jsonCommandLoader = jsonCommandLoader;
    }

    @Override
    public Boolean hasCode(String code) {
        return jsonCommandLoader.getCommand(PlatformTypes.tuya, CategoryTypes.DLQ, "fvu9fdxjyotgpzcc", code).isPresent();
    }

    @Override
    public Object sendCommandAction(String resourceId, String code, Object value) {
        return ReflectUtil.invoke(tuyadlqfvu9fdxjyotgpzccAgg, CommandUtil.transformCommand(code), List.of(resourceId, value));
    }

    @Override
    public List<DeviceControlCommandBo> querySupportedCommands() {
        return jsonCommandLoader.getCommands(PlatformTypes.tuya, CategoryTypes.DLQ, "fvu9fdxjyotgpzcc");
    }
}
