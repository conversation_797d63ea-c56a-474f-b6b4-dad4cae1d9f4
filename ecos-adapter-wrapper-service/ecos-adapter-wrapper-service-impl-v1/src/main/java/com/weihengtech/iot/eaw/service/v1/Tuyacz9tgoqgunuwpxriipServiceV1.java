package com.weihengtech.iot.eaw.service.v1;

import cn.hutool.core.util.ReflectUtil;
import com.weihengtech.iot.eaw.aggregation.tuya.Tuyacz9tgoqgunuwpxriipAgg;
import com.weihengtech.iot.eaw.service.api.AbstractCommandService;
import com.weihengtech.iot.eaw.service.api.consts.CategoryTypes;
import com.weihengtech.iot.eaw.service.api.consts.PlatformTypes;
import com.weihengtech.iot.eaw.service.api.model.bo.DeviceControlCommandBo;
import com.weihengtech.iot.eaw.service.v1.consts.ServiceNames;
import com.weihengtech.iot.eaw.service.v1.util.CommandUtil;
import com.weihengtech.iot.eaw.service.v1.util.JsonCommandLoader;
import org.springframework.stereotype.Service;

import java.util.List;

@Service(PlatformTypes.tuya + CategoryTypes.CZ + "9tgoqgunuwpxriip" + ServiceNames.JOIN_NAME)
public class Tuyacz9tgoqgunuwpxriipServiceV1 extends AbstractCommandService {

    private final Tuyacz9tgoqgunuwpxriipAgg tuyacz9tgoqgunuwpxriipAgg;
    private final JsonCommandLoader jsonCommandLoader;

    public Tuyacz9tgoqgunuwpxriipServiceV1(Tuyacz9tgoqgunuwpxriipAgg tuyacz9tgoqgunuwpxriipAgg, JsonCommandLoader jsonCommandLoader) {
        this.tuyacz9tgoqgunuwpxriipAgg = tuyacz9tgoqgunuwpxriipAgg;
        this.jsonCommandLoader = jsonCommandLoader;
    }

    @Override
    public Boolean hasCode(String code) {
        return jsonCommandLoader.getCommand(PlatformTypes.tuya, CategoryTypes.CZ, "9tgoqgunuwpxriip", code).isPresent();
    }

    @Override
    public Object sendCommandAction(String resourceId, String code, Object value) {
        return ReflectUtil.invoke(tuyacz9tgoqgunuwpxriipAgg, CommandUtil.transformCommand(code), List.of(resourceId, value));
    }

    @Override
    public List<DeviceControlCommandBo> querySupportedCommands() {
        return jsonCommandLoader.getCommands(PlatformTypes.tuya, CategoryTypes.CZ, "9tgoqgunuwpxriip");
    }
}
