package com.weihengtech.iot.eaw.service.v1.util;

import com.weihengtech.iot.eaw.service.api.model.bo.DeviceControlCommandBo;

import java.util.List;
import java.util.Optional;

public interface ICommandLoader {

    /**
     * 获取Command对象
     * @param platform 平台
     * @param category 品类
     * @param pId 产品id
     * @param code 命令标识
     * @return com.weihengtech.iot.eaw.service.api.model.bo.DeviceControlCommandBo
     */
    Optional<DeviceControlCommandBo> getCommand(String platform, String category, String pId, String code);

    /**
     * 获取Commands列表
     * @param platform 平台
     * @param category 品类
     * @param pId 产品id
     * @return 命令列表
     */
    List<DeviceControlCommandBo> getCommands(String platform, String category, String pId);
}
