package com.weihengtech.iot.eaw.service.v1;

import cn.hutool.core.util.ReflectUtil;
import com.weihengtech.iot.eaw.aggregation.iot.Iotzycnsbb78a247d902c4c21a5732c4e201f4857Agg;
import com.weihengtech.iot.eaw.service.api.AbstractCommandService;
import com.weihengtech.iot.eaw.service.api.consts.CategoryTypes;
import com.weihengtech.iot.eaw.service.api.consts.PlatformTypes;
import com.weihengtech.iot.eaw.service.api.model.bo.DeviceControlCommandBo;
import com.weihengtech.iot.eaw.service.v1.consts.ServiceNames;
import com.weihengtech.iot.eaw.service.v1.util.CommandUtil;
import com.weihengtech.iot.eaw.service.v1.util.JsonCommandLoader;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.List;

@Service(PlatformTypes.iot + CategoryTypes.ZYCNSB + "b78a247d902c4c21a5732c4e201f4857" + ServiceNames.JOIN_NAME)
public class Iotzycnsbb78a247d902c4c21a5732c4e201f4857ServiceV1 extends AbstractCommandService {

    private final Iotzycnsbb78a247d902c4c21a5732c4e201f4857Agg agg;
    private final JsonCommandLoader jsonCommandLoader;

    public Iotzycnsbb78a247d902c4c21a5732c4e201f4857ServiceV1(@Qualifier("iotzycnsbb78a247d902c4c21a5732c4e201f4857Agg") Iotzycnsbb78a247d902c4c21a5732c4e201f4857Agg agg, JsonCommandLoader jsonCommandLoader) {
        this.agg = agg;
        this.jsonCommandLoader = jsonCommandLoader;
    }

    @Override
    public Boolean hasCode(String code) {
        return jsonCommandLoader.getCommand(PlatformTypes.iot, CategoryTypes.ZYCNSB, "b78a247d902c4c21a5732c4e201f4857", code).isPresent();
    }

    @Override
    public Object sendCommandAction(String resourceId, String code, Object value) {
        return ReflectUtil.invoke(agg, CommandUtil.transformCommand(code), List.of(resourceId, value));
    }

    @Override
    public List<DeviceControlCommandBo> querySupportedCommands() {
        return jsonCommandLoader.getCommands(PlatformTypes.iot, CategoryTypes.ZYCNSB, "b78a247d902c4c21a5732c4e201f4857");
    }
}
