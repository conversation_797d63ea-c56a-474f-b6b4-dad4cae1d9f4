package com.weihengtech.iot.eaw.service.v1;

import cn.hutool.core.util.ReflectUtil;
import com.weihengtech.iot.eaw.aggregation.elink.ELinkEnergyStorageAggregation;
import com.weihengtech.iot.eaw.service.api.AbstractCommandService;
import com.weihengtech.iot.eaw.service.api.consts.CategoryTypes;
import com.weihengtech.iot.eaw.service.api.consts.PlatformTypes;
import com.weihengtech.iot.eaw.service.api.model.bo.DeviceControlCommandBo;
import com.weihengtech.iot.eaw.service.v1.consts.ServiceNames;
import com.weihengtech.iot.eaw.service.v1.util.CommandUtil;
import com.weihengtech.iot.eaw.service.v1.util.JsonCommandLoader;
import org.springframework.stereotype.Service;

import java.util.List;

@Service(PlatformTypes.elink + CategoryTypes.CNSB + ServiceNames.JOIN_NAME)
public class ELinkEnergyStorageServiceV1 extends AbstractCommandService {

    private final ELinkEnergyStorageAggregation eLinkEnergyStorageAggregation;
    private final JsonCommandLoader jsonCommandLoader;

    public ELinkEnergyStorageServiceV1(JsonCommandLoader jsonCommandLoader, ELinkEnergyStorageAggregation eLinkEnergyStorageAggregation) {
        this.jsonCommandLoader = jsonCommandLoader;
        this.eLinkEnergyStorageAggregation = eLinkEnergyStorageAggregation;
    }

    @Override
    public Boolean hasCode(String code) {
        return jsonCommandLoader.getCommand(PlatformTypes.elink, CategoryTypes.CNSB, "", code).isPresent();
    }

    @Override
    public Object sendCommandAction(String resourceId, String code, Object value) {
        return ReflectUtil.invoke(eLinkEnergyStorageAggregation, CommandUtil.transformCommand(code), List.of(resourceId, value));
    }

    @Override
    public List<DeviceControlCommandBo> querySupportedCommands() {
        return jsonCommandLoader.getCommands(PlatformTypes.elink, CategoryTypes.CNSB, "");
    }
}
