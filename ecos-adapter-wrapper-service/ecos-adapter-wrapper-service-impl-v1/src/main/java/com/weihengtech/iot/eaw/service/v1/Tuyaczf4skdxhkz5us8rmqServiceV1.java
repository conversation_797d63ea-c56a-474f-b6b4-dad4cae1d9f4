package com.weihengtech.iot.eaw.service.v1;

import cn.hutool.core.util.ReflectUtil;
import com.weihengtech.iot.eaw.aggregation.tuya.Tuyaczf4skdxhkz5us8rmqAgg;
import com.weihengtech.iot.eaw.service.api.AbstractCommandService;
import com.weihengtech.iot.eaw.service.api.consts.CategoryTypes;
import com.weihengtech.iot.eaw.service.api.consts.PlatformTypes;
import com.weihengtech.iot.eaw.service.api.model.bo.DeviceControlCommandBo;
import com.weihengtech.iot.eaw.service.v1.consts.ServiceNames;
import com.weihengtech.iot.eaw.service.v1.util.CommandUtil;
import com.weihengtech.iot.eaw.service.v1.util.JsonCommandLoader;
import org.springframework.stereotype.Service;

import java.util.List;

@Service(PlatformTypes.tuya + CategoryTypes.CZ + "f4skdxhkz5us8rmq" + ServiceNames.JOIN_NAME)
public class Tuyaczf4skdxhkz5us8rmqServiceV1 extends AbstractCommandService {

    private final Tuyaczf4skdxhkz5us8rmqAgg tuyaczf4skdxhkz5us8rmqAgg;
    private final JsonCommandLoader jsonCommandLoader;

    public Tuyaczf4skdxhkz5us8rmqServiceV1(Tuyaczf4skdxhkz5us8rmqAgg tuyaczf4skdxhkz5us8rmqAgg, JsonCommandLoader jsonCommandLoader) {
        this.tuyaczf4skdxhkz5us8rmqAgg = tuyaczf4skdxhkz5us8rmqAgg;
        this.jsonCommandLoader = jsonCommandLoader;
    }

    @Override
    public Boolean hasCode(String code) {
        return jsonCommandLoader.getCommand(PlatformTypes.tuya, CategoryTypes.CZ, "f4skdxhkz5us8rmq", code).isPresent();
    }

    @Override
    public Object sendCommandAction(String resourceId, String code, Object value) {
        return ReflectUtil.invoke(tuyaczf4skdxhkz5us8rmqAgg, CommandUtil.transformCommand(code), List.of(resourceId, value));
    }

    @Override
    public List<DeviceControlCommandBo> querySupportedCommands() {
        return jsonCommandLoader.getCommands(PlatformTypes.tuya, CategoryTypes.CZ, "f4skdxhkz5us8rmq");

    }
}
