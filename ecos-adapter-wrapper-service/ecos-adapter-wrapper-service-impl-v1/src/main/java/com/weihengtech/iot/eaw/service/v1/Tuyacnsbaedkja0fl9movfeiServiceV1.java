package com.weihengtech.iot.eaw.service.v1;

import cn.hutool.core.util.ReflectUtil;
import com.weihengtech.iot.eaw.aggregation.tuya.Tuyacnsbaedkja0fl9movfeiAgg;
import com.weihengtech.iot.eaw.service.api.AbstractCommandService;
import com.weihengtech.iot.eaw.service.api.consts.CategoryTypes;
import com.weihengtech.iot.eaw.service.api.consts.PlatformTypes;
import com.weihengtech.iot.eaw.service.api.model.bo.DeviceControlCommandBo;
import com.weihengtech.iot.eaw.service.v1.consts.ServiceNames;
import com.weihengtech.iot.eaw.service.v1.util.CommandUtil;
import com.weihengtech.iot.eaw.service.v1.util.JsonCommandLoader;
import org.springframework.stereotype.Service;

import java.util.List;

@Service(PlatformTypes.tuya + CategoryTypes.CNSB + "aedkja0fl9movfei" + ServiceNames.JOIN_NAME)
public class Tuyacnsbaedkja0fl9movfeiServiceV1 extends AbstractCommandService {

    private final Tuyacnsbaedkja0fl9movfeiAgg tuyacnsbaedkja0fl9movfeiAgg;
    private final JsonCommandLoader jsonCommandLoader;

    public Tuyacnsbaedkja0fl9movfeiServiceV1(Tuyacnsbaedkja0fl9movfeiAgg tuyacnsbaedkja0fl9movfeiAgg, JsonCommandLoader jsonCommandLoader) {
        this.tuyacnsbaedkja0fl9movfeiAgg = tuyacnsbaedkja0fl9movfeiAgg;
        this.jsonCommandLoader = jsonCommandLoader;
    }

    @Override
    public Boolean hasCode(String code) {
        return jsonCommandLoader.getCommand(PlatformTypes.tuya, CategoryTypes.CNSB, "aedkja0fl9movfei", code).isPresent();
    }

    @Override
    public Object sendCommandAction(String resourceId, String code, Object value) {
        return ReflectUtil.invoke(tuyacnsbaedkja0fl9movfeiAgg, CommandUtil.transformCommand(code), List.of(resourceId, value));
    }

    @Override
    public List<DeviceControlCommandBo> querySupportedCommands() {
        return jsonCommandLoader.getCommands(PlatformTypes.tuya, CategoryTypes.CNSB, "aedkja0fl9movfei");
    }
}
