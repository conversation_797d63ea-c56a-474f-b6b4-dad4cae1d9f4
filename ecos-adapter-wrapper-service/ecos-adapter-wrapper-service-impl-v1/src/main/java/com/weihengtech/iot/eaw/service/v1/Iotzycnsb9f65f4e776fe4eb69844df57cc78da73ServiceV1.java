package com.weihengtech.iot.eaw.service.v1;

import cn.hutool.core.util.ReflectUtil;
import com.weihengtech.iot.eaw.aggregation.iot.Iotzycnsb9f65f4e776fe4eb69844df57cc78da73Agg;
import com.weihengtech.iot.eaw.service.api.AbstractCommandService;
import com.weihengtech.iot.eaw.service.api.consts.CategoryTypes;
import com.weihengtech.iot.eaw.service.api.consts.PlatformTypes;
import com.weihengtech.iot.eaw.service.api.model.bo.DeviceControlCommandBo;
import com.weihengtech.iot.eaw.service.v1.consts.ServiceNames;
import com.weihengtech.iot.eaw.service.v1.util.CommandUtil;
import com.weihengtech.iot.eaw.service.v1.util.JsonCommandLoader;
import org.springframework.stereotype.Service;

import java.util.List;

@Service(PlatformTypes.iot + CategoryTypes.ZYCNSB + "9f65f4e776fe4eb69844df57cc78da73" + ServiceNames.JOIN_NAME)
public class Iotzycnsb9f65f4e776fe4eb69844df57cc78da73ServiceV1 extends AbstractCommandService {
    private final Iotzycnsb9f65f4e776fe4eb69844df57cc78da73Agg agg;
    private final JsonCommandLoader jsonCommandLoader;

    public Iotzycnsb9f65f4e776fe4eb69844df57cc78da73ServiceV1(Iotzycnsb9f65f4e776fe4eb69844df57cc78da73Agg agg, JsonCommandLoader jsonCommandLoader) {
        this.agg = agg;
        this.jsonCommandLoader = jsonCommandLoader;
    }

    @Override
    public Boolean hasCode(String code) {
        return jsonCommandLoader.getCommand(PlatformTypes.iot, CategoryTypes.ZYCNSB, "9f65f4e776fe4eb69844df57cc78da73", code).isPresent();
    }

    @Override
    public Object sendCommandAction(String resourceId, String code, Object value) {
        return ReflectUtil.invoke(agg, CommandUtil.transformCommand(code), List.of(resourceId, value));
    }

    @Override
    public List<DeviceControlCommandBo> querySupportedCommands() {
        return jsonCommandLoader.getCommands(PlatformTypes.iot, CategoryTypes.ZYCNSB, "9f65f4e776fe4eb69844df57cc78da73");
    }
}
