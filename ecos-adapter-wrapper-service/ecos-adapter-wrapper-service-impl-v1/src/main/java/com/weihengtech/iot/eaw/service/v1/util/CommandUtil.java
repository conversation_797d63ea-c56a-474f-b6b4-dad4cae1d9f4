package com.weihengtech.iot.eaw.service.v1.util;

import java.util.Locale;

public class CommandUtil {
    public static String transformCommand(String code) {
        String[] cs = code.split("_");
        StringBuilder sb = new StringBuilder(cs[0]);
        for (int i = 1; i < cs.length; i++) {
            sb.append(cs[i].substring(0, 1).toUpperCase(Locale.ROOT)).append(cs[i].substring(1));
        }
        return sb.toString();
    }
}
