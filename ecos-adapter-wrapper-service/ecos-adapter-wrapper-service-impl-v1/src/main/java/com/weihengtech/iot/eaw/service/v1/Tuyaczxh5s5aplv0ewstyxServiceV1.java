package com.weihengtech.iot.eaw.service.v1;

import cn.hutool.core.util.ReflectUtil;
import com.weihengtech.iot.eaw.aggregation.tuya.Tuyaczxh5s5aplv0ewstyxAgg;
import com.weihengtech.iot.eaw.service.api.AbstractCommandService;
import com.weihengtech.iot.eaw.service.api.consts.CategoryTypes;
import com.weihengtech.iot.eaw.service.api.consts.PlatformTypes;
import com.weihengtech.iot.eaw.service.api.model.bo.DeviceControlCommandBo;
import com.weihengtech.iot.eaw.service.v1.consts.ServiceNames;
import com.weihengtech.iot.eaw.service.v1.util.CommandUtil;
import com.weihengtech.iot.eaw.service.v1.util.JsonCommandLoader;
import org.springframework.stereotype.Service;

import java.util.List;

@Service(PlatformTypes.tuya + CategoryTypes.CZ + "xh5s5aplv0ewstyx" + ServiceNames.JOIN_NAME)
public class Tuyaczxh5s5aplv0ewstyxServiceV1 extends AbstractCommandService {

    private final Tuyaczxh5s5aplv0ewstyxAgg tuyaczxh5s5aplv0ewstyxAgg;
    private final JsonCommandLoader jsonCommandLoader;

    public Tuyaczxh5s5aplv0ewstyxServiceV1(Tuyaczxh5s5aplv0ewstyxAgg tuyaczxh5s5aplv0ewstyxAgg, JsonCommandLoader jsonCommandLoader) {
        this.tuyaczxh5s5aplv0ewstyxAgg = tuyaczxh5s5aplv0ewstyxAgg;
        this.jsonCommandLoader = jsonCommandLoader;
    }

    @Override
    public Boolean hasCode(String code) {
        return jsonCommandLoader.getCommand(PlatformTypes.tuya, CategoryTypes.CZ, "xh5s5aplv0ewstyx", code).isPresent();
    }

    @Override
    public Object sendCommandAction(String resourceId, String code, Object value) {
        return ReflectUtil.invoke(tuyaczxh5s5aplv0ewstyxAgg, CommandUtil.transformCommand(code), List.of(resourceId, value));
    }

    @Override
    public List<DeviceControlCommandBo> querySupportedCommands() {
        return jsonCommandLoader.getCommands(PlatformTypes.tuya, CategoryTypes.CZ, "xh5s5aplv0ewstyx");

    }
}
