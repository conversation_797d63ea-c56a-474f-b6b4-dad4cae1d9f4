package com.weihengtech.iot.eaw.service.v1;

import cn.hutool.core.util.ReflectUtil;
import com.weihengtech.iot.eaw.aggregation.tuya.Tuyazcnbqbl3bgjhzblyqiiziAgg;
import com.weihengtech.iot.eaw.service.api.AbstractCommandService;
import com.weihengtech.iot.eaw.service.api.consts.CategoryTypes;
import com.weihengtech.iot.eaw.service.api.consts.PlatformTypes;
import com.weihengtech.iot.eaw.service.api.model.bo.DeviceControlCommandBo;
import com.weihengtech.iot.eaw.service.v1.consts.ServiceNames;
import com.weihengtech.iot.eaw.service.v1.util.CommandUtil;
import com.weihengtech.iot.eaw.service.v1.util.JsonCommandLoader;
import org.springframework.stereotype.Service;

import java.util.List;

@Service(PlatformTypes.tuya + CategoryTypes.ZCNBQ + "bl3bgjhzblyqiizi" + ServiceNames.JOIN_NAME)
public class Tuyazcnbqbl3bgjhzblyqiiziServiceV1 extends AbstractCommandService {

    private final Tuyazcnbqbl3bgjhzblyqiiziAgg tuyazcnbqbl3bgjhzblyqiiziAgg;
    private final JsonCommandLoader jsonCommandLoader;

    public Tuyazcnbqbl3bgjhzblyqiiziServiceV1(Tuyazcnbqbl3bgjhzblyqiiziAgg tuyazcnbqbl3bgjhzblyqiiziAgg, JsonCommandLoader jsonCommandLoader) {
        this.tuyazcnbqbl3bgjhzblyqiiziAgg = tuyazcnbqbl3bgjhzblyqiiziAgg;
        this.jsonCommandLoader = jsonCommandLoader;
    }

    @Override
    public Boolean hasCode(String code) {
        return jsonCommandLoader.getCommand(PlatformTypes.tuya, CategoryTypes.ZCNBQ, "bl3bgjhzblyqiizi", code).isPresent();
    }

    @Override
    public Object sendCommandAction(String resourceId, String code, Object value) {
        return ReflectUtil.invoke(tuyazcnbqbl3bgjhzblyqiiziAgg, CommandUtil.transformCommand(code), List.of(resourceId, value));
    }

    @Override
    public List<DeviceControlCommandBo> querySupportedCommands() {
        return jsonCommandLoader.getCommands(PlatformTypes.tuya, CategoryTypes.ZCNBQ, "bl3bgjhzblyqiizi");
    }
}
