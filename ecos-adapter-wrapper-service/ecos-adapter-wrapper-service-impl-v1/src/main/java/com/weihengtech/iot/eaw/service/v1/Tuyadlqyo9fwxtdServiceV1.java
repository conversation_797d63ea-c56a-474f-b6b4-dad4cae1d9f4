package com.weihengtech.iot.eaw.service.v1;

import cn.hutool.core.util.ReflectUtil;
import com.weihengtech.iot.eaw.aggregation.tuya.Tuyadlqyo9fwxtdAgg;
import com.weihengtech.iot.eaw.service.api.AbstractCommandService;
import com.weihengtech.iot.eaw.service.api.consts.CategoryTypes;
import com.weihengtech.iot.eaw.service.api.consts.PlatformTypes;
import com.weihengtech.iot.eaw.service.api.model.bo.DeviceControlCommandBo;
import com.weihengtech.iot.eaw.service.v1.consts.ServiceNames;
import com.weihengtech.iot.eaw.service.v1.util.CommandUtil;
import com.weihengtech.iot.eaw.service.v1.util.JsonCommandLoader;
import org.springframework.stereotype.Service;

import java.util.List;

@Service(PlatformTypes.tuya + CategoryTypes.DLQ + "yo9fwxtd" + ServiceNames.JOIN_NAME)
public class Tuyadlqyo9fwxtdServiceV1 extends AbstractCommandService {

    private final Tuyadlqyo9fwxtdAgg tuyadlqyo9fwxtdAgg;
    private final JsonCommandLoader jsonCommandLoader;

    public Tuyadlqyo9fwxtdServiceV1(Tuyadlqyo9fwxtdAgg tuyadlqyo9fwxtdAgg, JsonCommandLoader jsonCommandLoader) {
        this.tuyadlqyo9fwxtdAgg = tuyadlqyo9fwxtdAgg;
        this.jsonCommandLoader = jsonCommandLoader;
    }

    @Override
    public Boolean hasCode(String code) {
        return jsonCommandLoader.getCommand(PlatformTypes.tuya, CategoryTypes.DLQ, "yo9fwxtd", code).isPresent();
    }

    @Override
    public Object sendCommandAction(String resourceId, String code, Object value) {
        return ReflectUtil.invoke(tuyadlqyo9fwxtdAgg, CommandUtil.transformCommand(code), List.of(resourceId, value));
    }

    @Override
    public List<DeviceControlCommandBo> querySupportedCommands() {
        return jsonCommandLoader.getCommands(PlatformTypes.tuya, CategoryTypes.DLQ, "yo9fwxtd");
    }
}
