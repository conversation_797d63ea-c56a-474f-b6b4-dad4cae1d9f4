package com.weihengtech.iot.eaw.service.v1.util;

import com.weihengtech.iot.eaw.service.api.consts.PlatformTypes;
import com.weihengtech.iot.eaw.service.api.model.bo.DeviceControlCommandBo;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static com.weihengtech.iot.eaw.service.v1.reader.JsonEnvironmentReader.COMMANDS;

@Component
public class JsonCommandLoader implements ICommandLoader{

    @Override
    public Optional<DeviceControlCommandBo> getCommand(String platform, String category, String pId, String code) {
        if (
                !StringUtils.hasLength(platform) ||
                !StringUtils.hasLength(category) ||
                !StringUtils.hasLength(code) ||
                !COMMANDS.containsKey(platform + "-" + category + pId) ||
                !COMMANDS.get(platform + "-" + category + pId).containsKey(code)
        ) {
            return Optional.empty();
        }
        return Optional.ofNullable(COMMANDS.get(platform + "-" + category + pId).get(code));
    }

    @Override
    public List<DeviceControlCommandBo> getCommands(String platform, String category, String pId) {
        if (!StringUtils.hasLength(platform) || !StringUtils.hasLength(category)) return Collections.emptyList();
        return PlatformTypes.get(platform).map(p -> COMMANDS.get(p + "-" + category + pId).values().stream().toList()).orElse(Collections.emptyList());
    }
}
