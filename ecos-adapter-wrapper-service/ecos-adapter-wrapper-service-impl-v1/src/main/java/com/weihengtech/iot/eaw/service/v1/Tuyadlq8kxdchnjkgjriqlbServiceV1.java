package com.weihengtech.iot.eaw.service.v1;

import cn.hutool.core.util.ReflectUtil;
import com.weihengtech.iot.eaw.aggregation.tuya.Tuyadlq8kxdchnjkgjriqlbAgg;
import com.weihengtech.iot.eaw.service.api.AbstractCommandService;
import com.weihengtech.iot.eaw.service.api.consts.CategoryTypes;
import com.weihengtech.iot.eaw.service.api.consts.PlatformTypes;
import com.weihengtech.iot.eaw.service.api.model.bo.DeviceControlCommandBo;
import com.weihengtech.iot.eaw.service.v1.consts.ServiceNames;
import com.weihengtech.iot.eaw.service.v1.util.CommandUtil;
import com.weihengtech.iot.eaw.service.v1.util.JsonCommandLoader;
import org.springframework.stereotype.Service;

import java.util.List;

@Service(PlatformTypes.tuya + CategoryTypes.DLQ + "8kxdchnjkgjriqlb" + ServiceNames.JOIN_NAME)
public class Tuyadlq8kxdchnjkgjriqlbServiceV1 extends AbstractCommandService {
    private final Tuyadlq8kxdchnjkgjriqlbAgg tuyadlq8kxdchnjkgjriqlbAgg;
    private final JsonCommandLoader jsonCommandLoader;

    public Tuyadlq8kxdchnjkgjriqlbServiceV1(Tuyadlq8kxdchnjkgjriqlbAgg tuyadlq8kxdchnjkgjriqlbAgg, JsonCommandLoader jsonCommandLoader) {
        this.tuyadlq8kxdchnjkgjriqlbAgg = tuyadlq8kxdchnjkgjriqlbAgg;
        this.jsonCommandLoader = jsonCommandLoader;
    }

    @Override
    public Boolean hasCode(String code) {
        return jsonCommandLoader.getCommand(PlatformTypes.tuya, CategoryTypes.DLQ, "8kxdchnjkgjriqlb", code).isPresent();
    }

    @Override
    public Object sendCommandAction(String resourceId, String code, Object value) {
        return ReflectUtil.invoke(tuyadlq8kxdchnjkgjriqlbAgg, CommandUtil.transformCommand(code), List.of(resourceId, value));
    }

    @Override
    public List<DeviceControlCommandBo> querySupportedCommands() {
        return jsonCommandLoader.getCommands(PlatformTypes.tuya, CategoryTypes.DLQ, "8kxdchnjkgjriqlb");
    }
}
