package com.weihengtech.iot.eaw.service.v1.reader;

import cn.hutool.core.io.resource.ResourceUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.weihengtech.iot.eaw.service.api.model.bo.DeviceControlCommandBo;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class JsonEnvironmentReader implements ApplicationContextAware {

    public static Map<String, Map<String, DeviceControlCommandBo>> COMMANDS = new HashMap<>(16);

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        try {
            Resource[] resources = applicationContext.getResources("classpath*:commands/*.json");
            for (Resource resource : resources) {
                try {
                    String filename = resource.getFilename();
                    String json = ResourceUtil.readUtf8Str("commands/" + filename);
                    assert filename != null;
                    String name = filename.replace(".json", "");
                    JSONArray jsonArray = JSONUtil.parseArray(json);
                    List<DeviceControlCommandBo> list = JSONUtil.toList(jsonArray, DeviceControlCommandBo.class);
                    COMMANDS.put(name, list.parallelStream().collect(Collectors.toMap(DeviceControlCommandBo::getCode, bo -> bo)));
                } catch (Exception ignored) {}
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
}
