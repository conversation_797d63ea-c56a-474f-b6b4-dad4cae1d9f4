package com.weihengtech.iot.eaw.service.v1;

import cn.hutool.core.util.ReflectUtil;
import com.weihengtech.iot.eaw.aggregation.tuya.Tuyapcfcd3ryfw7jl45vm4Agg;
import com.weihengtech.iot.eaw.service.api.AbstractCommandService;
import com.weihengtech.iot.eaw.service.api.consts.CategoryTypes;
import com.weihengtech.iot.eaw.service.api.consts.PlatformTypes;
import com.weihengtech.iot.eaw.service.api.model.bo.DeviceControlCommandBo;
import com.weihengtech.iot.eaw.service.v1.consts.ServiceNames;
import com.weihengtech.iot.eaw.service.v1.util.CommandUtil;
import com.weihengtech.iot.eaw.service.v1.util.JsonCommandLoader;
import org.springframework.stereotype.Service;

import java.util.List;

@Service(PlatformTypes.tuya + CategoryTypes.PC + "fcd3ryfw7jl45vm4" + ServiceNames.JOIN_NAME)
public class Tuyapcfcd3ryfw7jl45vm4ServiceV1 extends AbstractCommandService {

    private final Tuyapcfcd3ryfw7jl45vm4Agg tuyapcfcd3ryfw7jl45vm4Agg;
    private final JsonCommandLoader jsonCommandLoader;

    public Tuyapcfcd3ryfw7jl45vm4ServiceV1(Tuyapcfcd3ryfw7jl45vm4Agg tuyapcfcd3ryfw7jl45vm4Agg, JsonCommandLoader jsonCommandLoader) {
        this.tuyapcfcd3ryfw7jl45vm4Agg = tuyapcfcd3ryfw7jl45vm4Agg;
        this.jsonCommandLoader = jsonCommandLoader;
    }

    @Override
    public Boolean hasCode(String code) {
        return jsonCommandLoader.getCommand(PlatformTypes.tuya, CategoryTypes.PC, "fcd3ryfw7jl45vm4", code).isPresent();
    }

    @Override
    public Object sendCommandAction(String resourceId, String code, Object value) {
        return ReflectUtil.invoke(tuyapcfcd3ryfw7jl45vm4Agg, CommandUtil.transformCommand(code), List.of(resourceId, value));
    }

    @Override
    public List<DeviceControlCommandBo> querySupportedCommands() {
        return jsonCommandLoader.getCommands(PlatformTypes.tuya, CategoryTypes.PC, "fcd3ryfw7jl45vm4");
    }
}
