package com.weihengtech.iot.eaw.service.v1;


import cn.hutool.core.util.ReflectUtil;
import com.weihengtech.iot.eaw.aggregation.tuya.Tuyacz0faj5nss7aejoy6tAgg;
import com.weihengtech.iot.eaw.service.api.AbstractCommandService;
import com.weihengtech.iot.eaw.service.api.consts.CategoryTypes;
import com.weihengtech.iot.eaw.service.api.consts.PlatformTypes;
import com.weihengtech.iot.eaw.service.api.model.bo.DeviceControlCommandBo;
import com.weihengtech.iot.eaw.service.v1.consts.ServiceNames;
import com.weihengtech.iot.eaw.service.v1.util.CommandUtil;
import com.weihengtech.iot.eaw.service.v1.util.JsonCommandLoader;
import org.springframework.stereotype.Service;

import java.util.List;

@Service(PlatformTypes.tuya + CategoryTypes.CZ + "0faj5nss7aejoy6t" + ServiceNames.JOIN_NAME)
public class Tuyacz0faj5nss7aejoy6tServiceV1 extends AbstractCommandService {

    private final Tuyacz0faj5nss7aejoy6tAgg tuyacz0faj5nss7aejoy6tAgg;
    private final JsonCommandLoader jsonCommandLoader;

    public Tuyacz0faj5nss7aejoy6tServiceV1(Tuyacz0faj5nss7aejoy6tAgg tuyacz0faj5nss7aejoy6tAgg, JsonCommandLoader jsonCommandLoader) {
        this.tuyacz0faj5nss7aejoy6tAgg = tuyacz0faj5nss7aejoy6tAgg;
        this.jsonCommandLoader = jsonCommandLoader;
    }

    @Override
    public Boolean hasCode(String code) {
        return jsonCommandLoader.getCommand(PlatformTypes.tuya, CategoryTypes.CZ, "0faj5nss7aejoy6t", code).isPresent();
    }

    @Override
    public Object sendCommandAction(String resourceId, String code, Object value) {
        return ReflectUtil.invoke(tuyacz0faj5nss7aejoy6tAgg, CommandUtil.transformCommand(code), List.of(resourceId, value));
    }

    @Override
    public List<DeviceControlCommandBo> querySupportedCommands() {
        return jsonCommandLoader.getCommands(PlatformTypes.tuya, CategoryTypes.CZ, "0faj5nss7aejoy6t");
    }
}
