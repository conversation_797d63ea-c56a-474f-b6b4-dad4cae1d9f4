package com.weihengtech.iot.eaw.service.v1;

import cn.hutool.core.util.ReflectUtil;
import com.weihengtech.iot.eaw.aggregation.tuya.Tuyazcnbqvgghlrv53hatszdeAgg;
import com.weihengtech.iot.eaw.service.api.AbstractCommandService;
import com.weihengtech.iot.eaw.service.api.consts.CategoryTypes;
import com.weihengtech.iot.eaw.service.api.consts.PlatformTypes;
import com.weihengtech.iot.eaw.service.api.model.bo.DeviceControlCommandBo;
import com.weihengtech.iot.eaw.service.v1.consts.ServiceNames;
import com.weihengtech.iot.eaw.service.v1.util.CommandUtil;
import com.weihengtech.iot.eaw.service.v1.util.JsonCommandLoader;
import org.springframework.stereotype.Service;

import java.util.List;

@Service(PlatformTypes.tuya + CategoryTypes.ZCNBQ + "vgghlrv53hatszde" + ServiceNames.JOIN_NAME)
public class Tuyazcnbqvgghlrv53hatszdeServiceV1 extends AbstractCommandService {

    private final Tuyazcnbqvgghlrv53hatszdeAgg tuyazcnbvgghlrv53hatszdeAgg;
    private final JsonCommandLoader jsonCommandLoader;

    public Tuyazcnbqvgghlrv53hatszdeServiceV1(Tuyazcnbqvgghlrv53hatszdeAgg tuyazcnbvgghlrv53hatszdeAgg, JsonCommandLoader jsonCommandLoader) {
        this.tuyazcnbvgghlrv53hatszdeAgg = tuyazcnbvgghlrv53hatszdeAgg;
        this.jsonCommandLoader = jsonCommandLoader;
    }

    @Override
    public Boolean hasCode(String code) {
        return jsonCommandLoader.getCommand(PlatformTypes.tuya, CategoryTypes.ZCNBQ, "vgghlrv53hatszde", code).isPresent();
    }

    @Override
    public Object sendCommandAction(String resourceId, String code, Object value) {
        return ReflectUtil.invoke(tuyazcnbvgghlrv53hatszdeAgg, CommandUtil.transformCommand(code), List.of(resourceId, value));
    }

    @Override
    public List<DeviceControlCommandBo> querySupportedCommands() {
        return jsonCommandLoader.getCommands(PlatformTypes.tuya, CategoryTypes.ZCNBQ, "vgghlrv53hatszde");
    }
}
