package com.weihengtech.iot.eaw.service.v1;

import cn.hutool.core.util.ReflectUtil;
import com.weihengtech.iot.eaw.aggregation.tuya.Tuyadlqk72d1hdcq4mj3tucAgg;
import com.weihengtech.iot.eaw.service.api.AbstractCommandService;
import com.weihengtech.iot.eaw.service.api.consts.CategoryTypes;
import com.weihengtech.iot.eaw.service.api.consts.PlatformTypes;
import com.weihengtech.iot.eaw.service.api.model.bo.DeviceControlCommandBo;
import com.weihengtech.iot.eaw.service.v1.consts.ServiceNames;
import com.weihengtech.iot.eaw.service.v1.util.CommandUtil;
import com.weihengtech.iot.eaw.service.v1.util.JsonCommandLoader;
import org.springframework.stereotype.Service;

import java.util.List;

@Service(PlatformTypes.tuya + CategoryTypes.DLQ + "k72d1hdcq4mj3tuc" + ServiceNames.JOIN_NAME)
public class Tuyadlqk72d1hdcq4mj3tucServiceV1 extends AbstractCommandService {
    private final Tuyadlqk72d1hdcq4mj3tucAgg tuyadlqk72d1hdcq4mj3tucAgg;
    private final JsonCommandLoader jsonCommandLoader;

    public Tuyadlqk72d1hdcq4mj3tucServiceV1(Tuyadlqk72d1hdcq4mj3tucAgg tuyadlqk72d1hdcq4mj3tucAgg, JsonCommandLoader jsonCommandLoader) {
        this.tuyadlqk72d1hdcq4mj3tucAgg = tuyadlqk72d1hdcq4mj3tucAgg;
        this.jsonCommandLoader = jsonCommandLoader;
    }

    @Override
    public Boolean hasCode(String code) {
        return jsonCommandLoader.getCommand(PlatformTypes.tuya, CategoryTypes.DLQ, "k72d1hdcq4mj3tuc", code).isPresent();
    }

    @Override
    public Object sendCommandAction(String resourceId, String code, Object value) {
        return ReflectUtil.invoke(tuyadlqk72d1hdcq4mj3tucAgg, CommandUtil.transformCommand(code), List.of(resourceId, value));
    }

    @Override
    public List<DeviceControlCommandBo> querySupportedCommands() {
        return jsonCommandLoader.getCommands(PlatformTypes.tuya, CategoryTypes.DLQ, "k72d1hdcq4mj3tuc");
    }
}
