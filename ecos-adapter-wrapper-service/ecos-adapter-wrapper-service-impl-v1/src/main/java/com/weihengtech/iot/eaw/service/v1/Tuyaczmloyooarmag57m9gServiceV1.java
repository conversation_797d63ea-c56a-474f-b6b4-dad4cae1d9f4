package com.weihengtech.iot.eaw.service.v1;

import cn.hutool.core.util.ReflectUtil;
import com.weihengtech.iot.eaw.aggregation.tuya.Tuyaczmloyooarmag57m9gAgg;
import com.weihengtech.iot.eaw.service.api.AbstractCommandService;
import com.weihengtech.iot.eaw.service.api.consts.CategoryTypes;
import com.weihengtech.iot.eaw.service.api.consts.PlatformTypes;
import com.weihengtech.iot.eaw.service.api.model.bo.DeviceControlCommandBo;
import com.weihengtech.iot.eaw.service.v1.consts.ServiceNames;
import com.weihengtech.iot.eaw.service.v1.util.CommandUtil;
import com.weihengtech.iot.eaw.service.v1.util.JsonCommandLoader;
import org.springframework.stereotype.Service;

import java.util.List;

@Service(PlatformTypes.tuya + CategoryTypes.CZ + "mloyooarmag57m9g" + ServiceNames.JOIN_NAME)
public class Tuyaczmloyooarmag57m9gServiceV1 extends AbstractCommandService {

    private final Tuyaczmloyooarmag57m9gAgg tuyaczmloyooarmag57m9gAgg;
    private final JsonCommandLoader jsonCommandLoader;

    public Tuyaczmloyooarmag57m9gServiceV1(Tuyaczmloyooarmag57m9gAgg tuyaczmloyooarmag57m9gAgg, JsonCommandLoader jsonCommandLoader) {
        this.tuyaczmloyooarmag57m9gAgg = tuyaczmloyooarmag57m9gAgg;
        this.jsonCommandLoader = jsonCommandLoader;
    }

    @Override
    public Boolean hasCode(String code) {
        return jsonCommandLoader.getCommand(PlatformTypes.tuya, CategoryTypes.CZ, "mloyooarmag57m9g", code).isPresent();
    }

    @Override
    public Object sendCommandAction(String resourceId, String code, Object value) {
        return ReflectUtil.invoke(tuyaczmloyooarmag57m9gAgg, CommandUtil.transformCommand(code), List.of(resourceId, value));
    }

    @Override
    public List<DeviceControlCommandBo> querySupportedCommands() {
        return jsonCommandLoader.getCommands(PlatformTypes.tuya, CategoryTypes.CZ, "mloyooarmag57m9g");

    }
}
