package com.weihengtech.iot.eaw.service.v1;

import cn.hutool.core.util.ReflectUtil;
import com.weihengtech.iot.eaw.aggregation.iot.Iotzycnsbaac31c57ae8b456b989b903440e0750dAgg;
import com.weihengtech.iot.eaw.service.api.AbstractCommandService;
import com.weihengtech.iot.eaw.service.api.consts.CategoryTypes;
import com.weihengtech.iot.eaw.service.api.consts.PlatformTypes;
import com.weihengtech.iot.eaw.service.api.model.bo.DeviceControlCommandBo;
import com.weihengtech.iot.eaw.service.v1.consts.ServiceNames;
import com.weihengtech.iot.eaw.service.v1.util.CommandUtil;
import com.weihengtech.iot.eaw.service.v1.util.JsonCommandLoader;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.List;

@Service(PlatformTypes.iot + CategoryTypes.ZYCNSB + "aac31c57ae8b456b989b903440e0750d" + ServiceNames.JOIN_NAME)
public class Iotzycnsbaac31c57ae8b456b989b903440e0750dServiceV1  extends AbstractCommandService {
    private final Iotzycnsbaac31c57ae8b456b989b903440e0750dAgg agg;
    private final JsonCommandLoader jsonCommandLoader;

    public Iotzycnsbaac31c57ae8b456b989b903440e0750dServiceV1(@Qualifier("iotzycnsbaac31c57ae8b456b989b903440e0750dAgg") Iotzycnsbaac31c57ae8b456b989b903440e0750dAgg agg, JsonCommandLoader jsonCommandLoader) {
        this.agg = agg;
        this.jsonCommandLoader = jsonCommandLoader;
    }

    @Override
    public Boolean hasCode(String code) {
        return jsonCommandLoader.getCommand(PlatformTypes.iot, CategoryTypes.ZYCNSB, "aac31c57ae8b456b989b903440e0750d", code).isPresent();
    }

    @Override
    public Object sendCommandAction(String resourceId, String code, Object value) {
        return ReflectUtil.invoke(agg, CommandUtil.transformCommand(code), List.of(resourceId, value));
    }

    @Override
    public List<DeviceControlCommandBo> querySupportedCommands() {
        return jsonCommandLoader.getCommands(PlatformTypes.iot, CategoryTypes.ZYCNSB, "aac31c57ae8b456b989b903440e0750d");
    }
}
