package com.weihengtech.iot.eaw.service.api;

import com.weihengtech.iot.eaw.service.api.model.bo.DeviceControlCommandBo;

import java.util.List;

public interface ICommandService {

    /**
     * 查询平台支持的命令
     * @return 平台支持的命令列表
     */
    List<DeviceControlCommandBo> querySupportedCommands();

    /**
     * 下发命令
     * @param resourceId 资源id
     * @param code    命令标识
     * @param value  命令体
     * @return 返回值
     */
    Object sendCommand(String resourceId, String code, Object value);
}
