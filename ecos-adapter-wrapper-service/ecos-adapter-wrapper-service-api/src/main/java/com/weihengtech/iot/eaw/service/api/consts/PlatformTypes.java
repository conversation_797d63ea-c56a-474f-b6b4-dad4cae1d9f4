package com.weihengtech.iot.eaw.service.api.consts;

import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

public class PlatformTypes {
    public static final String elink = "elink";
    public static final String tuya = "tuya";
    public static final String ocpp = "ocpp";
    public static final String iot = "iot";

    public static Optional<String> get(String sourceType) {
        Class<PlatformTypes> clazz = PlatformTypes.class;
        try {
            Field field = clazz.getDeclaredField(sourceType);
            return Optional.ofNullable(field.get(null).toString());
        } catch (NoSuchFieldException | IllegalAccessException e) {
            return Optional.empty();
        }
    }

    public static List<String> getTypeNames() {
        Class<PlatformTypes> clazz = PlatformTypes.class;
        Field[] declaredFields = clazz.getDeclaredFields();
        return Arrays.stream(declaredFields).map(Field::getName).collect(Collectors.toList());
    }
}
