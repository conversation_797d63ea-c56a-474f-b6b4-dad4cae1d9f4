package com.weihengtech.iot.eaw.service.api.model.bo;

public class DeviceControlCommandBo {
    /**
     * 命令标识
     */
    private String code;
    /**
     * 类型
     */
    private String type;
    /**
     * 命令值
     */
    private Object value;
    /**
     * 描述
     */
    private String description;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Object getValue() {
        return value;
    }

    public void setValue(Object value) {
        this.value = value;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}
