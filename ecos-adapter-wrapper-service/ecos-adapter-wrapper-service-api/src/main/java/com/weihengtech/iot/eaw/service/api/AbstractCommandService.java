package com.weihengtech.iot.eaw.service.api;

import com.weihengtech.iot.eaw.service.api.exception.ServiceCommandNotSupportedException;
import com.weihengtech.iot.eaw.service.api.model.bo.DeviceControlCommandBo;

import java.util.List;

public abstract class AbstractCommandService implements ICommandService {

    public abstract Boolean hasCode(String code);
    public abstract Object sendCommandAction(String resourceId, String code, Object value);
    public abstract List<DeviceControlCommandBo> querySupportedCommands();

    @Override
    public Object sendCommand(String resourceId, String code, Object value) {
        if (hasCode(code)) {
            return sendCommandAction(resourceId, code, value);
        }
        throw new ServiceCommandNotSupportedException("[id]: " + resourceId + " send [" + code + "]" + " with value [" + value + "]");
    }
}
