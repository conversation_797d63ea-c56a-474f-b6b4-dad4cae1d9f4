package com.weihengtech.iot.eaw.aggregation.api.ocpp;

import java.util.List;

public interface IOcppAgg {

    /**
     * 重置
     */
    Boolean chargePointReset(List<Object> params);

    /**
     * 开始充电
     */
    Boolean startCharging(List<Object> params);

    /**
     * 结束充电
     */
    Boolean stopCharging(List<Object> params);

    /**
     * 充电点状态
     */
    Object chargePointStatus(List<Object> params);

    /**
     * 充电点信息
     */
    Object chargePointInfo(List<Object> params);

    /**
     * 新增卡片
     */
    Boolean addCard(List<Object> params);

    /**
     * 移除卡片
     */
    Boolean removeCards(List<Object> params);

    /**
     * 覆盖卡片
     */
    Boolean coverCards(List<Object> params);

    /**
     * 查询绑卡
     */
    Object queryCardsList(List<Object> params);

    /**
     * 充电桩默认配置
     */
    Boolean chargePointDefaultProfile(List<Object> params);

    /**
     * 充电桩阈值配置
     */
    Boolean chargePointMaxProfile(List<Object> params);

    /**
     * 充电桩固件状态
     */
    Object chargePointFirmwareStatus(List<Object> params);

    /**
     * 充电桩固件升级
     */
    Boolean chargePointFirmwareUpdate(List<Object> params);

    /**
     * 充电桩关闭负载均衡
     */
    Boolean chargePointShutdownLoadBalance(List<Object> params);

    /**
     * 充电桩生成诊断日志
     */
    Boolean chargePointGetDiagnostics(List<Object> params);

    /**
     * 获取充电桩全局配置
     */
    Object getConfiguration(List<Object> params);

    /**
     * 更新充电桩全局配置
     */
    Boolean updConfiguration(List<Object> params);

    /**
     * 充电桩透传指令
     */
    Boolean chargePointDataTransfer(List<Object> params);
}
