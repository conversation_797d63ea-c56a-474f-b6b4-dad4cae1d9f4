package com.weihengtech.iot.eaw.aggregation.api.iot;

import com.weihengtech.iot.eaw.aggregation.api.ICloudAgg;

import java.util.List;

public interface IIotCloudAgg extends ICloudAgg {

    /**
     * 查询单设备属性
     */
    Object querySingleDeviceProperty(List<Object> params);

    /**
     * 查询多设备属性
     */
    List<Object> queryBatchDeviceProperty(List<Object> params);

    /**
     * 降采样查询
     */
    Object queryWithSample(List<Object> params);

    /**
     * 原始数据查询
     */
    Object queryWithoutSample(List<Object> params);

    /**
     * 查询最后一个点
     */
    Object queryLastPoint(List<Object> params);

    /**
     * 差值查询
     */
    Object queryDelta(List<Object> params);

    /**
     * 查询设备事件
     */
    Object queryDeviceEvent(List<Object> params);

    /**
     * 查询网关升级状态
     */
    String queryOtaStatus(List<Object> params);

    /**
     * 查询网关历史升级记录
     */
    Object queryOtaHistory(List<Object> params);

    /**
     * 查询网关升级固件列表
     */
    Object listOtaFirmware(List<Object> params);
}
