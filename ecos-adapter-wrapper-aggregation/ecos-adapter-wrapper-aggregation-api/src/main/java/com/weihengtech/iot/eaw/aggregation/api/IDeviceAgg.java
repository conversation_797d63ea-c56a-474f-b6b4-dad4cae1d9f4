package com.weihengtech.iot.eaw.aggregation.api;

import java.util.List;

public interface IDeviceAgg {

    /**
     * 查询设备分类
     */
    String queryDeviceCategory(List<Object> params);

    /**
     * 查询设备ip
     */
    String queryDeviceIp(List<Object> params);

    /**
     * 查询设备详情
     */
    Object querySingleDeviceDetail(List<Object> params);

    /**
     * 批量查询设备详情
     */
    List<Object> queryBatchDeviceDetail(List<Object> params);
}
