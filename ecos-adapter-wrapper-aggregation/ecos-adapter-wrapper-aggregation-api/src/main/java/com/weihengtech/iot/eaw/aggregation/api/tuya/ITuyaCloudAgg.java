package com.weihengtech.iot.eaw.aggregation.api.tuya;

import com.weihengtech.iot.eaw.aggregation.api.ICloudAgg;

import java.util.List;

public interface ITuyaCloudAgg extends ICloudAgg {

    /**
     * 新增家庭成员
     */
    Object addHomeMember(List<Object> params);

    /**
     * 查询家庭成员
     */
    Object queryHomeMembers(List<Object> params);

    /**
     * 删除家庭成员
     */
    Object deleteHomeMember(List<Object> params);

    /**
     * 查询单设备属性
     */
    Object querySingleDeviceProperty(List<Object> params);

    /**
     * 查询多设备属性
     */
    List<Object> queryBatchDeviceProperty(List<Object> params);

    /**
     * 通过tuyaId获取HomeId
     */
    String queryHomeIdByTuyaId(List<Object> params);

    /**
     * 获取设备操作日志
     */
    Object queryDeviceLogs(List<Object> params);
}
