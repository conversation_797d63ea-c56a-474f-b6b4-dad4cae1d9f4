package com.weihengtech.iot.eaw.aggregation.api.iot;

import java.util.List;

public interface IIotzycnsb9f65f4e776fe4eb69844df57cc78da73Agg {

    /**
     * 重置设备
     */
    Boolean reset(List<Object> params);

    /**
     * 透传读
     */
    List<Integer> transportRead(List<Object> params);

    /**
     * 透传写
     */
    Boolean transportWrite(List<Object> params);

    /**
     * 批量透传读
     */
    Object batchTransportRead(List<Object> params);

    /**
     * 批量透传写
     */
    Boolean batchTransportWrite(List<Object> params);

    /**
     * 加速采集
     */
    Boolean speedup(List<Object> params);

    /**
     * ota升级
     */
    Boolean upgrade(List<Object> params);

    /**
     * ota升级v2版本：返回taskId
     */
    String upgradeV2(List<Object> params);
}
