package com.weihengtech.iot.eaw.aggregation.api.tuya;

import java.util.List;

public interface ITuyaczo9pyieorAgg {

    /**
     * 控制设备的开启与关闭
     */
    Boolean switchButton(List<Object> params);

    /**
     * 倒计时开关
     */
    Boolean countdown(List<Object> params);

    /**
     * 上电状态
     */
    Boolean relayStatus(List<Object> params);

    /**
     * 指示灯状态
     */
    Boolean lightMode(List<Object> params);

    /**
     * 童锁
     */
    Boolean childLock(List<Object> params);
}
