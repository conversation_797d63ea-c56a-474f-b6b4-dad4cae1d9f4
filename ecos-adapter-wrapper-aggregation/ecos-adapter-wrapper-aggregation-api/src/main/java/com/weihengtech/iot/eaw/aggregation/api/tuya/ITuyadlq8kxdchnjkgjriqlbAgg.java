package com.weihengtech.iot.eaw.aggregation.api.tuya;

import java.util.List;

public interface ITuyadlq8kxdchnjkgjriqlbAgg {

    /**
     * 断路器开关
     */
    Boolean switchButton(List<Object> params);

    /**
     * 告警设置1
     */
    Boolean alarmSetOne(List<Object> params);

    /**
     * 告警设置2
     */
    Boolean alarmSetTwo(List<Object> params);

    /**
     * 自检测试
     */
    Boolean leakageCurrTest(List<Object> params);

    /**
     * 定期自检设置
     */
    Boolean leakageCurrTestSet(List<Object> params);
}
