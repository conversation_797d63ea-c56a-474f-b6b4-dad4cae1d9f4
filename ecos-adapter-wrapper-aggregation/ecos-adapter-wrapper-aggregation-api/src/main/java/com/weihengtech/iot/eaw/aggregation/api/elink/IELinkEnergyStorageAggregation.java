package com.weihengtech.iot.eaw.aggregation.api.elink;

import java.util.List;

public interface IELinkEnergyStorageAggregation {

    /**
     * 重置设备
     */
    Boolean reset(List<Object> params);

    /**
     * 更新设备
     */
    Boolean upgrade(List<Object> params);

    /**
     * 透传读
     */
    List<Integer> transportRead(List<Object> params);

    /**
     * 透传写
     */
    Boolean transportWrite(List<Object> params);

    /**
     * 加速采集
     */
    Boolean speedup(List<Object> params);
}
