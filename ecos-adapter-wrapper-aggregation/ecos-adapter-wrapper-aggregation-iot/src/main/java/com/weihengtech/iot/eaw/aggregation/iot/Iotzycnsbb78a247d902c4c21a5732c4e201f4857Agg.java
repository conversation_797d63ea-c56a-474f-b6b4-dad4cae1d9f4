package com.weihengtech.iot.eaw.aggregation.iot;

import com.weihengtech.iot.eaw.aggregation.api.exception.AggregationRequestFailureException;
import com.weihengtech.iot.eaw.aggregation.api.iot.IIotzycnsbb78a247d902c4c21a5732c4e201f4857Agg;
import com.weihengtech.iot.eaw.repo.iot.feign.IotAdapterFeign;
import com.weihengtech.pub.infra.utils.annotation.ThrowUniformly;
import org.springframework.stereotype.Component;

@Component
@ThrowUniformly(AggregationRequestFailureException.class)
public class Iotzycnsbb78a247d902c4c21a5732c4e201f4857Agg extends Iotzycnsbaac31c57ae8b456b989b903440e0750dAgg implements IIotzycnsbb78a247d902c4c21a5732c4e201f4857Agg {
    public Iotzycnsbb78a247d902c4c21a5732c4e201f4857Agg(IotAdapterFeign iotAdapterFeign) {
        super(iotAdapterFeign);
    }
}
