package com.weihengtech.iot.eaw.aggregation.iot;

import com.weihengtech.iot.eaw.aggregation.api.exception.AggregationRequestFailureException;
import com.weihengtech.iot.eaw.aggregation.api.iot.IIotzycnsb9f65f4e776fe4eb69844df57cc78da73Agg;
import com.weihengtech.iot.eaw.repo.iot.feign.IotAdapterFeign;
import com.weihengtech.pub.infra.utils.annotation.ThrowUniformly;
import org.springframework.stereotype.Component;


@Component
@ThrowUniformly(AggregationRequestFailureException.class)
public class Iotzycnsb9f65f4e776fe4eb69844df57cc78da73Agg extends Iotzycnsbaac31c57ae8b456b989b903440e0750dAgg implements IIotzycnsb9f65f4e776fe4eb69844df57cc78da73Agg {

    public Iotzycnsb9f65f4e776fe4eb69844df57cc78da73Agg(IotAdapterFeign iotAdapterFeign) {
        super(iotAdapterFeign);
    }
}
