package com.weihengtech.iot.eaw.aggregation.iot;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.weihengtech.iot.eaw.aggregation.api.exception.AggregationRequestFailureException;
import com.weihengtech.iot.eaw.aggregation.api.iot.IIotzycnsbaac31c57ae8b456b989b903440e0750dAgg;
import com.weihengtech.iot.eaw.repo.iot.feign.IotAdapterFeign;
import com.weihengtech.iot.eaw.repo.iot.model.dto.IotBatchTransportReadDto;
import com.weihengtech.iot.eaw.repo.iot.model.dto.IotBatchTransportWriteDto;
import com.weihengtech.iot.eaw.repo.iot.model.dto.IotTransportReadDto;
import com.weihengtech.iot.eaw.repo.iot.model.dto.IotTransportWriteDto;
import com.weihengtech.pub.infra.resp.model.R;
import com.weihengtech.pub.infra.utils.annotation.ThrowUniformly;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

@Component
@ThrowUniformly(AggregationRequestFailureException.class)
public class Iotzycnsbaac31c57ae8b456b989b903440e0750dAgg implements IIotzycnsbaac31c57ae8b456b989b903440e0750dAgg {

    private final IotAdapterFeign iotAdapterFeign;

    public Iotzycnsbaac31c57ae8b456b989b903440e0750dAgg(IotAdapterFeign iotAdapterFeign) {
        this.iotAdapterFeign = iotAdapterFeign;
    }

    @Override
    public Boolean reset(List<Object> params) {
        String deviceId = (String) Objects.requireNonNull(params.get(0));
        R r = iotAdapterFeign.resetDevice(deviceId);
        String code = r.getOrDefault("code", 500).toString();
        return "200".equals(code);
    }

    @Override
    public List<Integer> transportRead(List<Object> params) {
        String deviceId = Objects.requireNonNull((String) params.get(0));
        JSONObject json = Objects.requireNonNull(JSONUtil.parseObj(params.get(1)));
        Integer functionCode = Objects.requireNonNull(json.getInt("func_code"));
        Integer slaveId = Objects.requireNonNull(json.getInt("slave_id"));
        Integer start = Objects.requireNonNull(json.getInt("start"));
        Integer length = Objects.requireNonNull(json.getInt("len"));
        IotTransportReadDto iotTransportReadDto = new IotTransportReadDto();
        iotTransportReadDto.setDeviceSn(deviceId);
        iotTransportReadDto.setFunctionCode(functionCode);
        iotTransportReadDto.setSlaveId(slaveId);
        iotTransportReadDto.setStartAddress(start);
        iotTransportReadDto.setQuantity(length);
        R r = iotAdapterFeign.transportRead(iotTransportReadDto);
        String code = r.getOrDefault("code", 500).toString();
        if ("200".equals(code)) {
            Object o = r.get("data");
            if (o instanceof List<?> os) {
                return os.stream().map(obj -> (Integer) obj).toList();
            }
        }
        throw new RuntimeException("request failed");
    }

    @Override
    public Boolean transportWrite(List<Object> params) {
        String deviceId = Objects.requireNonNull((String) params.get(0));
        JSONObject json = Objects.requireNonNull(JSONUtil.parseObj(params.get(1)));
        Integer slaveId = Objects.requireNonNull(json.getInt("slave_id"));
        Integer start = Objects.requireNonNull(json.getInt("start"));
        Integer length = Objects.requireNonNull(json.getInt("len"));
        List<Integer> values = Objects.requireNonNull(json.getBeanList("values", Integer.class));
        IotTransportWriteDto iotTransportWriteDto = new IotTransportWriteDto();
        iotTransportWriteDto.setDeviceSn(deviceId);
        iotTransportWriteDto.setSlaveId(slaveId);
        iotTransportWriteDto.setStartAddress(start);
        iotTransportWriteDto.setByteLength(length);
        iotTransportWriteDto.setValue(values);
        iotTransportWriteDto.setFunctionCode(16);
        R r = iotAdapterFeign.transportWrite(iotTransportWriteDto);
        String code = r.getOrDefault("code", 500).toString();
        return "200".equals(code);
    }

    @Override
    public Object batchTransportRead(List<Object> params) {
        String deviceId = (String) Objects.requireNonNull(params.get(0));
        JSONArray jsonArray = JSONUtil.parseArray(Objects.requireNonNull(params.get(1)));
        List<IotBatchTransportReadDto> dtoList = jsonArray.stream().map(obj -> {
            JSONObject jsonObject = Objects.requireNonNull(JSONUtil.parseObj(obj));
            IotBatchTransportReadDto dto = new IotBatchTransportReadDto();
            dto.setFunctionCode(Objects.requireNonNull(jsonObject.getInt("func_code")));
            dto.setQuantity(Objects.requireNonNull(Objects.requireNonNull(jsonObject.getInt("len"))));
            dto.setSlaveId(Objects.requireNonNull(jsonObject.getInt("slave_id")));
            dto.setStartAddress(Objects.requireNonNull(jsonObject.getInt("start")));
            dto.setIndex(Objects.requireNonNull(jsonObject.getInt("index")));
            return dto;
        }).toList();
        R r = iotAdapterFeign.batchTransportRead(dtoList, deviceId);
        String code = r.getOrDefault("code", 500).toString();
        if ("200".equals(code)) {
            return r.get("data");
        }
        throw new RuntimeException("request failed");
    }

    @Override
    public Boolean batchTransportWrite(List<Object> params) {
        String deviceId = (String) Objects.requireNonNull(params.get(0));
        JSONArray jsonArray = JSONUtil.parseArray(Objects.requireNonNull(params.get(1)));
        List<IotBatchTransportWriteDto> dtoList = jsonArray.stream().map(obj -> {
            JSONObject jsonObject = Objects.requireNonNull(JSONUtil.parseObj(obj));
            IotBatchTransportWriteDto dto = new IotBatchTransportWriteDto();
            dto.setByteLength(Objects.requireNonNull(jsonObject.getInt("len")));
            dto.setSlaveId(Objects.requireNonNull(jsonObject.getInt("slave_id")));
            dto.setStartAddress(Objects.requireNonNull(jsonObject.getInt("start")));
            dto.setFunctionCode(Objects.requireNonNull(jsonObject.getInt("func_code")));
            dto.setValue(Objects.requireNonNull(jsonObject.getJSONArray("values")).toList(Integer.class));
            dto.setIndex(Objects.requireNonNull(jsonObject.getInt("index")));
            return dto;
        }).toList();
        R r = iotAdapterFeign.batchTransportWrite(dtoList, deviceId);
        String code = r.getOrDefault("code", 500).toString();
        return "200".equals(code);
    }

    @Override
    public Boolean speedup(List<Object> params) {
        String deviceId = (String) Objects.requireNonNull(params.get(0));
        R r = iotAdapterFeign.speedup(deviceId);
        String code = r.getOrDefault("code", 500).toString();
        return "200".equals(code);
    }

    @Override
    public Boolean upgrade(List<Object> params) {
        String deviceId = Objects.requireNonNull((String) params.get(0));
        JSONObject json = Objects.requireNonNull(JSONUtil.parseObj(params.get(1)));
        String url = Objects.requireNonNull(json.getStr("url"));
        R r = iotAdapterFeign.upgradeOTA(deviceId, url);
        String code = r.getOrDefault("code", 500).toString();
        if (code.equals("200")) {
            return Boolean.TRUE;
        }
        throw new RuntimeException("request error");
    }

    @Override
    public String upgradeV2(List<Object> params) {
        JSONObject json = Objects.requireNonNull(JSONUtil.parseObj(params.get(1)));
        String gateWayDeviceSn = Objects.requireNonNull(json.getStr("gateWayDeviceSn"));
        Boolean isGateWay = Objects.requireNonNull(json.getBool("isGateWay"));
        String otaUrl = Objects.requireNonNull(json.getStr("otaUrl"));
        R r = iotAdapterFeign.upgradeOtaV2(gateWayDeviceSn, isGateWay, otaUrl);
        String code = r.getOrDefault("code", 500).toString();
        if (code.equals("200")) {
            return String.valueOf(r.get("data"));
        }
        throw new RuntimeException("request error");
    }
}
