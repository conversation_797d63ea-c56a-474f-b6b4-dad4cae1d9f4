package com.weihengtech.iot.eaw.aggregation.iot;

import com.weihengtech.iot.eaw.aggregation.api.exception.AggregationRequestFailureException;
import com.weihengtech.iot.eaw.aggregation.api.iot.IIotzycnsbb4666ebf9e8a46b69fe50f61d74b6a36Agg;
import com.weihengtech.iot.eaw.repo.iot.feign.IotAdapterFeign;
import com.weihengtech.pub.infra.utils.annotation.ThrowUniformly;
import org.springframework.stereotype.Component;

@Component
@ThrowUniformly(AggregationRequestFailureException.class)
public class Iotzycnsbb4666ebf9e8a46b69fe50f61d74b6a36Agg extends Iotzycnsbaac31c57ae8b456b989b903440e0750dAgg implements IIotzycnsbb4666ebf9e8a46b69fe50f61d74b6a36Agg {
    public Iotzycnsbb4666ebf9e8a46b69fe50f61d74b6a36Agg(IotAdapterFeign iotAdapterFeign) {
        super(iotAdapterFeign);
    }
}
