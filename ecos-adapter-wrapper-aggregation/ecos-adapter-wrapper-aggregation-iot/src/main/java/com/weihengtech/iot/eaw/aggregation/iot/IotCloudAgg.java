package com.weihengtech.iot.eaw.aggregation.iot;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.weihengtech.iot.eaw.aggregation.api.exception.AggregationRequestFailureException;
import com.weihengtech.iot.eaw.aggregation.api.iot.IIotCloudAgg;
import com.weihengtech.iot.eaw.repo.iot.feign.IotAdapterFeign;
import com.weihengtech.iot.eaw.repo.iot.model.dto.IotDeltaQueryDto;
import com.weihengtech.iot.eaw.repo.iot.model.dto.IotLastPointDto;
import com.weihengtech.iot.eaw.repo.iot.model.dto.IotWithSampleQueryDto;
import com.weihengtech.iot.eaw.repo.iot.model.dto.IotWithoutSampleQueryDto;
import com.weihengtech.pub.infra.resp.model.R;
import com.weihengtech.pub.infra.utils.annotation.ThrowUniformly;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

@Component
@ThrowUniformly(AggregationRequestFailureException.class)
public class IotCloudAgg implements IIotCloudAgg {

    private final IotAdapterFeign iotAdapterFeign;

    public IotCloudAgg(IotAdapterFeign iotAdapterFeign) {
        this.iotAdapterFeign = iotAdapterFeign;
    }

    @Override
    public Object querySingleDeviceProperty(List<Object> params) {
        String id = (String) Objects.requireNonNull(params.get(0));
        R r = iotAdapterFeign.batchDeviceProperty(id);
        String code = r.getOrDefault("code", 500).toString();
        if (code.equals("200")) {
            Object data = r.get("data");
            JSONObject json = JSONUtil.parseObj(data);
            JSONArray jsonArray = json.getJSONArray(id);
            return jsonArray.stream().map(obj -> {
                JSONObject vo = (JSONObject) obj;
                JSONObject funcVo = vo.getJSONObject("funcVo");
                Map<String, Object> kv = new HashMap<>();
                kv.put(funcVo.getStr("funcKey"), vo.get("realTime"));
                return kv;
            }).toList();
        }
        throw new RuntimeException("request error");
    }

    @Override
    public List<Object> queryBatchDeviceProperty(List<Object> params) {
        if (params.get(1) instanceof List<?> deviceIds) {
            if (CollectionUtils.isEmpty(deviceIds)) return Collections.emptyList();
            R r = iotAdapterFeign.batchDeviceProperty(String.join(",", deviceIds.stream().map(String::valueOf).toList()));
            String code = r.getOrDefault("code", 500).toString();
            if (code.equals("200")) {
                Object data = r.get("data");
                JSONObject json = JSONUtil.parseObj(data);
                return json.keySet().stream().map(k -> {
                    JSONArray jsonArray = json.getJSONArray(k);
                    List<Map<String, Object>> kvs = jsonArray.stream().map(obj -> {
                        JSONObject vo = (JSONObject) obj;
                        JSONObject funcVo = vo.getJSONObject("funcVo");
                        Map<String, Object> kv = new HashMap<>();
                        kv.put(funcVo.getStr("funcKey"), vo.get("realTime"));
                        return kv;
                    }).toList();
                    Map<String, Object> res = new HashMap<>();
                    res.put("id", k);
                    res.put("status", kvs);
                    return res;
                }).collect(Collectors.toList());

            }
        }
        throw new RuntimeException("request error");
    }

    @Override
    public Object queryWithSample(List<Object> params) {
        String deviceId = Objects.requireNonNull((String) params.get(0));
        JSONObject json = Objects.requireNonNull(JSONUtil.parseObj(params.get(1)));
        Long end = Objects.requireNonNull(json.getLong("end"));
        Long start = Objects.requireNonNull(json.getLong("start"));
        String times = Objects.requireNonNull(json.getStr("times"));
        JSONArray metricList = Objects.requireNonNull(json.getJSONArray("metric_list"));
        String aggregateFunction = json.getStr("aggregateFunction", "first");
        String index = json.getStr("index", "0");
        IotWithSampleQueryDto dto = new IotWithSampleQueryDto();
        dto.setDeviceSn(deviceId);
        dto.setEnd(end);
        dto.setStart(start);
        dto.setTimes(times);
        dto.setMetricList(metricList.toList(String.class));
        dto.setAggregateFunction(aggregateFunction);
        dto.setIndex(index);
        R r = iotAdapterFeign.withSampleQuery(dto);
        String code = r.getOrDefault("code", 500).toString();
        if (code.equals("200")) {
            return r.get("data");
        }
        throw new RuntimeException("request error");
    }

    @Override
    public Object queryWithoutSample(List<Object> params) {
        String deviceId = Objects.requireNonNull((String) params.get(0));
        JSONObject json = Objects.requireNonNull(JSONUtil.parseObj(params.get(1)));
        Long end = Objects.requireNonNull(json.getLong("end"));
        Long start = Objects.requireNonNull(json.getLong("start"));
        JSONArray metricList = Objects.requireNonNull(json.getJSONArray("metric_list"));
        String index = json.getStr("index", "0");
        IotWithoutSampleQueryDto dto = new IotWithoutSampleQueryDto();
        dto.setDeviceSn(deviceId);
        dto.setEnd(end);
        dto.setStart(start);
        dto.setMetricList(metricList.toList(String.class));
        dto.setIndex(index);
        R r = iotAdapterFeign.withoutSampleQuery(dto);
        String code = r.getOrDefault("code", 500).toString();
        if (code.equals("200")) {
            return r.get("data");
        }
        throw new RuntimeException("request error");
    }

    @Override
    public Object queryLastPoint(List<Object> params) {
        String deviceId = Objects.requireNonNull((String) params.get(0));
        JSONObject json = Objects.requireNonNull(JSONUtil.parseObj(params.get(1)));
        Long end = Objects.requireNonNull(json.getLong("end"));
        Long start = Objects.requireNonNull(json.getLong("start"));
        JSONArray metricList = Objects.requireNonNull(json.getJSONArray("metric_list"));
        String index = json.getStr("index", "0");
        IotLastPointDto dto = new IotLastPointDto();
        dto.setDeviceSn(deviceId);
        dto.setEnd(end);
        dto.setStart(start);
        dto.setMetricList(metricList.toList(String.class));
        dto.setIndex(index);
        R r = iotAdapterFeign.lastPoint(dto);
        String code = r.getOrDefault("code", 500).toString();
        if (code.equals("200")) {
            return r.get("data");
        }
        throw new RuntimeException("request error");
    }

    @Override
    public Object queryDelta(List<Object> params) {
        String deviceId = Objects.requireNonNull((String) params.get(0));
        JSONObject json = Objects.requireNonNull(JSONUtil.parseObj(params.get(1)));
        Long end = Objects.requireNonNull(json.getLong("end"));
        Long start = Objects.requireNonNull(json.getLong("start"));
        String times = Objects.requireNonNull(json.getStr("times"));
        JSONArray metricList = Objects.requireNonNull(json.getJSONArray("metric_list"));
        String index = json.getStr("index", "0");
        IotDeltaQueryDto dto = new IotDeltaQueryDto();
        dto.setDeviceSn(deviceId);
        dto.setEnd(end);
        dto.setStart(start);
        dto.setTimes(times);
        dto.setMetricList(metricList.toList(String.class));
        dto.setIndex(index);
        R r = iotAdapterFeign.deltaQuery(dto);
        String code = r.getOrDefault("code", 500).toString();
        if (code.equals("200")) {
            return r.get("data");
        }
        throw new RuntimeException("request error");
    }

    @Override
    public Object queryDeviceEvent(List<Object> params) {
        String deviceId = Objects.requireNonNull((String) params.get(0));
        JSONObject json = Objects.requireNonNull(JSONUtil.parseObj(params.get(1)));
        Long start = Objects.requireNonNull(json.getLong("start"));
        Long end = Objects.requireNonNull(json.getLong("end"));
        Integer page = Objects.requireNonNull(json.getInt("page"));
        Integer size = Objects.requireNonNull(json.getInt("size"));
        String eventType = Objects.requireNonNull(json.getStr("eventType"));
        String eventName = json.getStr("eventName", "");
        R r = iotAdapterFeign.queryDeviceEvent(deviceId, start, end, page, size, eventType, eventName);
        String code = r.getOrDefault("code", 500).toString();
        if (code.equals("200")) {
            return r.get("data");
        }
        throw new RuntimeException("request error");
    }

    @Override
    public String queryOtaStatus(List<Object> params) {
        String deviceSn = Objects.requireNonNull((String) params.get(0));
        String taskId = Objects.requireNonNull((String) params.get(1));
        R r = iotAdapterFeign.queryOtaStatus(deviceSn, taskId);
        String code = r.getOrDefault("code", 500).toString();
        if (code.equals("200")) {
            return (String) r.get("data");
        }
        throw new RuntimeException("request error");
    }

    @Override
    public Object queryOtaHistory(List<Object> params) {
        String deviceSn = Objects.requireNonNull((String) params.get(0));
        R r = iotAdapterFeign.queryOtaHistory(deviceSn);
        String code = r.getOrDefault("code", 500).toString();
        if (code.equals("200")) {
            return r.get("data");
        }
        throw new RuntimeException("request error");
    }

    @Override
    public Object listOtaFirmware(List<Object> params) {
        String productName = Objects.requireNonNull((String) params.get(0));
        R r = iotAdapterFeign.listOtaFirmware(productName);
        String code = r.getOrDefault("code", 500).toString();
        if (code.equals("200")) {
            return r.get("data");
        }
        throw new RuntimeException("request error");
    }

    @Override
    public String queryDeviceCategory(List<Object> params) {
        Object o = Objects.requireNonNull(querySingleDeviceDetail(params));
        JSONObject json = JSONUtil.parseObj(o);
        return json.getStr("category");
    }

    @Override
    public String queryDeviceIp(List<Object> params) {
        Object o = Objects.requireNonNull(querySingleDeviceDetail(params));
        JSONObject json = JSONUtil.parseObj(o);
        return json.getStr("ip");
    }

    @Override
    public Object querySingleDeviceDetail(List<Object> params) {
        String id = (String) Objects.requireNonNull(params.get(0));
        R r = iotAdapterFeign.batchDeviceDetail(id);
        String code = r.getOrDefault("code", 500).toString();
        if (code.equals("200")) {
            Object data = r.get("data");
            if (data instanceof List<?> list) {
                return list.isEmpty() ? null : list.get(0);
            }
        }
        throw new RuntimeException("request error");
    }

    @Override
    public List<Object> queryBatchDeviceDetail(List<Object> params) {
        if (params.get(1) instanceof List<?> deviceIds) {
            if (CollectionUtils.isEmpty(deviceIds)) return Collections.emptyList();
            R r = iotAdapterFeign.batchDeviceDetail(String.join(",", deviceIds.stream().map(String::valueOf).toList()));
            String code = r.getOrDefault("code", 500).toString();
            if (code.equals("200")) {
                Object o = r.get("data");
                if (o instanceof List<?> os) {
                    return os.stream().map(obj -> (Object) obj).toList();
                }
            }
        }
        throw new RuntimeException("request error");
    }

    @Override
    public Boolean isOnline(List<Object> params) {
        Object o = Objects.requireNonNull(querySingleDeviceDetail(params));
        JSONObject json = JSONUtil.parseObj(o);
        return json.getStr("onlineStatus").equals("online");
    }

    @Override
    public List<String> queryAllOnlineDevice(List<Object> params) {
        R r = iotAdapterFeign.allOnlineDevices();
        String code = r.getOrDefault("code", 500).toString();
        if (code.equals("200")) {
            Object o = r.get("data");
            if (o instanceof List<?> os) {
                return os.stream().map(obj -> (String) obj).toList();
            }
        }
        return List.of();
    }
}
