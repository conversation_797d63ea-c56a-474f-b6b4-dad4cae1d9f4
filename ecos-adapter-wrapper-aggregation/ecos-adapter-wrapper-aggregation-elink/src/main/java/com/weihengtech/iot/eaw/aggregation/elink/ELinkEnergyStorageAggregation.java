package com.weihengtech.iot.eaw.aggregation.elink;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.weihengtech.iot.eaw.aggregation.api.elink.IELinkEnergyStorageAggregation;
import com.weihengtech.iot.eaw.aggregation.api.exception.AggregationRequestFailureException;
import com.weihengtech.iot.eaw.repo.elink.cloud.AliCloudRepo;
import com.weihengtech.iot.eaw.repo.elink.feign.ELinkAdapterFeign;
import com.weihengtech.iot.eaw.repo.elink.model.dto.ELinkParamDto;
import com.weihengtech.pub.infra.utils.annotation.ThrowUniformly;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

@ThrowUniformly(AggregationRequestFailureException.class)
public class ELinkEnergyStorageAggregation implements IELinkEnergyStorageAggregation {

    private final ELinkAdapterFeign eLinkAdapterFeign;
    private final AliCloudRepo aliCloudRepo;

    public ELinkEnergyStorageAggregation(ELinkAdapterFeign eLinkAdapterFeign, AliCloudRepo aliCloudRepo) {
        this.eLinkAdapterFeign = eLinkAdapterFeign;
        this.aliCloudRepo = aliCloudRepo;
    }

    @Override
    public Boolean reset(List<Object> params) {
        try {
            aliCloudRepo.reset((String) params.get(0));
            return true;
        } catch (Exception e) {
            throw new RuntimeException(e.getMessage());
        }
    }

    @Override
    public Boolean upgrade(List<Object> params) {
        try {
            String deviceId = (String) params.get(0);
            JSONObject json = JSONUtil.parseObj(params.get(1));
            ELinkParamDto eLinkParamDto = new ELinkParamDto();
            eLinkParamDto.setArgs(Arrays.asList(deviceId, String.valueOf(json.getOrDefault("url", ""))));
            return isSuccess(eLinkAdapterFeign.upgrade(eLinkParamDto));
        } catch (Exception e) {
            throw new RuntimeException(e.getMessage());
        }
    }

    @Override
    public List<Integer> transportRead(List<Object> params) {
        try {
            String deviceId = (String) params.get(0);
            JSONObject jsonObject = JSONUtil.parseObj(params.get(1));
            Integer slaveId = Objects.requireNonNull(jsonObject.getInt("slave_id"));
            Integer start = Objects.requireNonNull(jsonObject.getInt("start"));
            Integer length = Objects.requireNonNull(jsonObject.getInt("len"));

            ELinkParamDto eLinkParamDto = new ELinkParamDto();
            eLinkParamDto.setArgs(Arrays.asList(deviceId, slaveId, start, length));
            JSONObject resp = eLinkAdapterFeign.read(eLinkParamDto);
            if (isSuccess(resp)) {
                Object o = resp.get("result");
                if (o instanceof List<?> os) {
                    return os.stream().map(obj -> (Integer)obj).toList();
                }
            }
            return Collections.emptyList();
        } catch (Exception e) {
            throw new RuntimeException(e.getMessage());
        }
    }

    @Override
    public Boolean transportWrite(List<Object> params) {
        try {
            String deviceId = (String) params.get(0);
            JSONObject jsonObject = JSONUtil.parseObj(params.get(1));
            List<Integer> values = Objects.requireNonNull(jsonObject.getBeanList("values", Integer.class));
            Integer slaveId = Objects.requireNonNull(jsonObject.getInt("slave_id"));
            Integer start = Objects.requireNonNull(jsonObject.getInt("start"));
            Integer length = Objects.requireNonNull(jsonObject.getInt("len"));
            ELinkParamDto eLinkParamDto = new ELinkParamDto();
            eLinkParamDto.setArgs(Arrays.asList(deviceId, slaveId, start, length, values));
            return isSuccess(eLinkAdapterFeign.write(eLinkParamDto));
        } catch (Exception e) {
            throw new RuntimeException(e.getMessage());
        }
    }

    @Override
    public Boolean speedup(List<Object> params) {
        try {
            ELinkParamDto eLinkParamDto = new ELinkParamDto();
            eLinkParamDto.setArgs(Collections.singletonList(params.get(0)));
            JSONObject resp = eLinkAdapterFeign.speedup(eLinkParamDto);
            return isSuccess(resp);
        } catch (Exception e) {
            throw new RuntimeException(e.getMessage());
        }
    }

    private Boolean isSuccess(JSONObject jsonObject) {
        final String success = "SUCCESS";
        final String pending = "PENDING";
        final String failedResp = "false";
        String state = jsonObject.getOrDefault("state", "FAILURE").toString();
        String result = jsonObject.getOrDefault("result", "false").toString();
        return pending.equals(state) || (success.equals(state) && !result.equals(failedResp));
    }
}
