package com.weihengtech.iot.eaw.aggregation.elink;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.weihengtech.iot.eaw.aggregation.api.elink.IELinkCloudAggregation;
import com.weihengtech.iot.eaw.aggregation.api.exception.AggregationRequestFailureException;
import com.weihengtech.iot.eaw.repo.elink.cloud.AliCloudRepo;
import com.weihengtech.pub.infra.utils.annotation.ThrowUniformly;

import java.util.List;

@ThrowUniformly(AggregationRequestFailureException.class)
public class ELinkCloudAggregation implements IELinkCloudAggregation {

    private final AliCloudRepo aliCloudRepo;

    public ELinkCloudAggregation(AliCloudRepo aliCloudRepo) {
        this.aliCloudRepo = aliCloudRepo;
    }

    @Override
    public Object querySingleDeviceDetail(List<Object> params) {
        try {
            return aliCloudRepo.querySingleDeviceDetail((String) params.get(0));
        } catch (Exception e) {
            throw new RuntimeException(e.getMessage());
        }
    }

    @Override
    public List<Object> queryBatchDeviceDetail(List<Object> params) {
        Object o = params.get(1);
        if (o instanceof List<?> os) {
            try {
                return aliCloudRepo.queryBatchDeviceDetail(os.stream().map(obj -> (String) obj).toList());
            } catch (Exception e) {
                throw new RuntimeException(e.getMessage());
            }
        } else {
            throw new RuntimeException("param error");
        }
    }

    @Override
    public Boolean isOnline(List<Object> params) {
        try {
            return aliCloudRepo.isOnline((String) params.get(0));
        } catch (Exception e) {
            throw new RuntimeException(e.getMessage());
        }
    }

    @Override
    public String queryDeviceIp(List<Object> params) {
        try {
            Object o = querySingleDeviceDetail(params);
            JSONObject json = JSONUtil.parseObj(o);
            return json.getStr("ipAddress");
        } catch (Exception e) {
            throw new RuntimeException(e.getMessage());
        }
    }

    @Override
    public List<String> queryAllOnlineDevice(List<Object> params) {
        try {
            return aliCloudRepo.queryAllOnlineDevice();
        } catch (Exception e) {
            throw new RuntimeException(e.getMessage());
        }
    }

    @Override
    public String queryDeviceCategory(List<Object> params) {
        try {
            Object o = querySingleDeviceDetail(params);
            JSONObject json = JSONUtil.parseObj(o);
            return json.getStr("category");
        } catch (Exception e) {
            throw new RuntimeException(e.getMessage());
        }
    }
}
