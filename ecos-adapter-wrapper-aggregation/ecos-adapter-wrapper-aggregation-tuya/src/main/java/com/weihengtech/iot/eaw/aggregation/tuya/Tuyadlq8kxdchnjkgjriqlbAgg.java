package com.weihengtech.iot.eaw.aggregation.tuya;

import cn.hutool.core.codec.Base64;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.weihengtech.iot.eaw.aggregation.api.exception.AggregationRequestFailureException;
import com.weihengtech.iot.eaw.aggregation.api.tuya.ITuyadlq8kxdchnjkgjriqlbAgg;
import com.weihengtech.iot.eaw.aggregation.tuya.utils.RequestUtil;
import com.weihengtech.iot.eaw.repo.tuya.feign.TuyaAdapterFeign;
import com.weihengtech.pub.infra.utils.annotation.ThrowUniformly;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

@Component
@ThrowUniformly(AggregationRequestFailureException.class)
public class Tuyadlq8kxdchnjkgjriqlbAgg extends RequestUtil implements ITuyadlq8kxdchnjkgjriqlbAgg {

    public Tuyadlq8kxdchnjkgjriqlbAgg(TuyaAdapterFeign tuyaAdapterFeign) {
        super(tuyaAdapterFeign);
    }

    @Override
    public Boolean switchButton(List<Object> params) {
        String deviceId = (String) Objects.requireNonNull(params.get(0));
        Boolean switchFlag = (Boolean) Objects.requireNonNull(params.get(1));
        return sendCommand(deviceId, "switch", switchFlag);
    }

    @Override
    public Boolean alarmSetOne(List<Object> params) {
        String deviceId = (String) Objects.requireNonNull(params.get(0));
        byte[] bytes = parseParamToAlarmSet(params);
        if (bytes.length == 0) {
            throw new RuntimeException("request error");
        }
        return sendCommand(deviceId, "alarm_set_1", Base64.encode(bytes));
    }

    @Override
    public Boolean alarmSetTwo(List<Object> params) {
        String deviceId = (String) Objects.requireNonNull(params.get(0));
        byte[] bytes = parseParamToAlarmSet(params);
        if (bytes.length == 0) {
            throw new RuntimeException("request error");
        }
        return sendCommand(deviceId, "alarm_set_2", Base64.encode(bytes));
    }

    private byte[] parseParamToAlarmSet(List<Object> params) {
        if (params.get(1) instanceof List<?> alarms) {
            if (alarms.isEmpty()) return new byte[0];
            byte[] bytes = new byte[alarms.size()*4];
            for (int i = 0; i < alarms.size(); i++) {
                Object o = alarms.get(i);
                JSONObject json = JSONUtil.parseObj(Objects.requireNonNull(o));
                int code = Objects.requireNonNull(json.getInt("code"));
                boolean switchBtn = json.getBool("switch", false);
                int threshold = json.getInt("threshold", 0);
                bytes[i * 4] = (byte) code;
                bytes[i * 4 + 1] = (byte) (switchBtn ? 0x01 : 0x00);
                bytes[i * 4 + 2] = (byte) ((threshold >> 8) & 0xFF);
                bytes[i * 4 + 3] = (byte) (threshold & 0xFF);
            }
            return bytes;
        }
        return new byte[0];
    }

    @Override
    public Boolean leakageCurrTest(List<Object> params) {
        String deviceId = (String) Objects.requireNonNull(params.get(0));
        Boolean switchFlag = (Boolean) Objects.requireNonNull(params.get(1));
        return sendCommand(deviceId, "leakagecurr_test", switchFlag);
    }

    @Override
    public Boolean leakageCurrTestSet(List<Object> params) {
        String deviceId = Objects.requireNonNull((String) params.get(0));
        JSONObject json = JSONUtil.parseObj(Objects.requireNonNull(params.get(1)));
        boolean switchBtn = json.getBool("switch", false);
        int date = json.getInt("date", 1);
        int hour = json.getInt("hour", 8);
        byte[] bytes = new byte[3];
        bytes[0] = (byte) (switchBtn ? 0x01 : 0x00);
        bytes[1] = (byte) date;
        bytes[2] = (byte) hour;
        return sendCommand(deviceId, "leakagecurr_test_set", Base64.encode(bytes));
    }
}
