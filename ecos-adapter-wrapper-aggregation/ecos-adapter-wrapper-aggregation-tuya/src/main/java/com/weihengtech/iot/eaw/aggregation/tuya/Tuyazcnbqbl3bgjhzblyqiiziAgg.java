package com.weihengtech.iot.eaw.aggregation.tuya;

import com.weihengtech.iot.eaw.aggregation.api.exception.AggregationRequestFailureException;
import com.weihengtech.iot.eaw.aggregation.api.tuya.ITuyazcnbqbl3bgjhzblyqiiziAgg;
import com.weihengtech.iot.eaw.repo.tuya.feign.TuyaAdapterFeign;
import com.weihengtech.pub.infra.utils.annotation.ThrowUniformly;
import org.springframework.stereotype.Component;

@Component
@ThrowUniformly(AggregationRequestFailureException.class)
public class Tuyazcnbqbl3bgjhzblyqiiziAgg extends Tuyacnsbaedkja0fl9movfeiAgg implements ITuyazcnbqbl3bgjhzblyqiiziAgg {
    public Tuyazcnbqbl3bgjhzblyqiiziAgg(TuyaAdapterFeign tuyaAdapterFeign) {
        super(tuyaAdapterFeign);
    }
}
