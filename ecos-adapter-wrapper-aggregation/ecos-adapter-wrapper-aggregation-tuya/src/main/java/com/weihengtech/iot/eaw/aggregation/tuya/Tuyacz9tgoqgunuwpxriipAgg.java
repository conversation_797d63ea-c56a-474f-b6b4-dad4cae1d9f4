package com.weihengtech.iot.eaw.aggregation.tuya;

import com.weihengtech.iot.eaw.aggregation.api.exception.AggregationRequestFailureException;
import com.weihengtech.iot.eaw.aggregation.api.tuya.ITuyacz9tgoqgunuwpxriipAgg;
import com.weihengtech.iot.eaw.repo.tuya.feign.TuyaAdapterFeign;
import com.weihengtech.pub.infra.utils.annotation.ThrowUniformly;
import org.springframework.stereotype.Component;

@Component
@ThrowUniformly(AggregationRequestFailureException.class)
public class Tuyacz9tgoqgunuwpxriipAgg  extends Tuyacz0faj5nss7aejoy6tAgg implements ITuyacz9tgoqgunuwpxriipAgg {
    public Tuyacz9tgoqgunuwpxriipAgg(TuyaAdapterFeign tuyaAdapterFeign) {
        super(tuyaAdapterFeign);
    }
}
