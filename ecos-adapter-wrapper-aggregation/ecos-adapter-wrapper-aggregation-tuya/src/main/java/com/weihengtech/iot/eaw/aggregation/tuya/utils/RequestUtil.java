package com.weihengtech.iot.eaw.aggregation.tuya.utils;

import com.weihengtech.iot.eaw.repo.tuya.feign.TuyaAdapterFeign;
import com.weihengtech.iot.eaw.repo.tuya.model.dto.TuyaCommandsDto;
import com.weihengtech.pub.infra.resp.model.R;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class RequestUtil {
    private final TuyaAdapterFeign tuyaAdapterFeign;

    public RequestUtil(TuyaAdapterFeign tuyaAdapterFeign) {
        this.tuyaAdapterFeign = tuyaAdapterFeign;
    }

    protected Boolean sendCommand(String deviceId, String code, Object value) {
        TuyaCommandsDto tuyaCommandsDto = new TuyaCommandsDto();
        tuyaCommandsDto.setDeviceId(deviceId);
        TuyaCommandsDto.Command command = new TuyaCommandsDto.Command();
        command.setCode(code);
        command.setValue(value);
        tuyaCommandsDto.setCommands(List.of(command));
        R r = tuyaAdapterFeign.sendCommands(tuyaCommandsDto);
        String responseCode = r.getOrDefault("code", 500).toString();
        return responseCode.equals("200");
    }
}

