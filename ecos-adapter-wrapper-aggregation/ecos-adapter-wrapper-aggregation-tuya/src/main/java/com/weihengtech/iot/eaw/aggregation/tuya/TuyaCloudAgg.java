package com.weihengtech.iot.eaw.aggregation.tuya;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.weihengtech.iot.eaw.aggregation.api.exception.AggregationRequestFailureException;
import com.weihengtech.iot.eaw.aggregation.api.tuya.ITuyaCloudAgg;
import com.weihengtech.iot.eaw.repo.tuya.feign.TuyaAdapterFeign;
import com.weihengtech.iot.eaw.repo.tuya.model.dto.TuyaAddMemberDto;
import com.weihengtech.pub.infra.resp.model.R;
import com.weihengtech.pub.infra.utils.annotation.ThrowUniformly;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

@Component
@ThrowUniformly(AggregationRequestFailureException.class)
public class TuyaCloudAgg implements ITuyaCloudAgg {

    private final TuyaAdapterFeign tuyaAdapterFeign;
    public TuyaCloudAgg(TuyaAdapterFeign tuyaAdapterFeign) {
        this.tuyaAdapterFeign = tuyaAdapterFeign;
    }

    public Object addHomeMember(List<Object> params) {
        String homeId = Objects.requireNonNull((String) params.get(0));
        JSONObject json = Objects.requireNonNull(JSONUtil.parseObj(params.get(1)));
        String appSchema = Objects.requireNonNull(json.getStr("app_schema"));
        String countryCode = Objects.requireNonNull(json.getStr("country_code"));
        String memberAccount = Objects.requireNonNull(json.getStr("member_account"));
        Boolean admin = Objects.requireNonNull(json.getBool("admin"));
        String name = Objects.requireNonNull(json.getStr("name"));
        TuyaAddMemberDto tuyaAddMemberDto = new TuyaAddMemberDto();
        tuyaAddMemberDto.setAppSchema(appSchema);
        TuyaAddMemberDto.TuyaHomeMemberInfo member = new TuyaAddMemberDto.TuyaHomeMemberInfo();
        member.setCountryCode(countryCode);
        member.setMemberAccount(memberAccount);
        member.setAdmin(admin);
        member.setName(name);
        tuyaAddMemberDto.setMember(member);
        R r = tuyaAdapterFeign.addHomeMember(Long.parseLong(homeId), tuyaAddMemberDto);
        if (r.getOrDefault("code", 500).toString().equals("200")) {
            return r.get("result");
        }
        throw new RuntimeException("request error");
    }

    public Object queryHomeMembers(List<Object> params) {
        String homeId = Objects.requireNonNull((String) params.get(0));
        R r = tuyaAdapterFeign.queryHomeMembers(Long.parseLong(homeId));
        if (r.getOrDefault("code", 500).toString().equals("200")) {
            return r.get("result");
        }
        throw new RuntimeException("request error");
    }

    public Object deleteHomeMember(List<Object> params) {
        String homeId = Objects.requireNonNull((String) params.get(0));
        String uid = Objects.requireNonNull((String) params.get(1));
        R r = tuyaAdapterFeign.deleteHomeMember(Long.parseLong(homeId), uid);
        if (r.getOrDefault("code", 500).toString().equals("200")) {
            return r.get("result");
        }
        throw new RuntimeException("request error");
    }

    @Override
    public Object querySingleDeviceProperty(List<Object> params) {
        String id = (String) Objects.requireNonNull(params.get(0));
        R r = tuyaAdapterFeign.querySingleDeviceProperty(id);
        String code = r.getOrDefault("code", 500).toString();
        if (code.equals("200")) {
            return r.get("data");
        }
        throw new RuntimeException("request error");
    }

    @Override
    public List<Object> queryBatchDeviceProperty(List<Object> params) {
        if (params.get(1) instanceof List<?> deviceIds) {
            if (CollectionUtils.isEmpty(deviceIds)) return Collections.emptyList();
            R r = tuyaAdapterFeign.queryBatchDeviceProperty(String.join(",", deviceIds.stream().map(String::valueOf).toList()));
            return parseBatchResult(r);
        }
        throw new RuntimeException("request error");
    }

    @Override
    public String queryHomeIdByTuyaId(List<Object> params) {
        String tuyaId = Objects.requireNonNull((String) params.get(0));
        R r = tuyaAdapterFeign.queryHomeDeviceDetail(tuyaId);
        String code = r.getOrDefault("code", 500).toString();
        if (code.equals("200")) {
            JSONObject json = JSONUtil.parseObj(r.get("result"));
            return json.getStr("ownerId");
        }
        throw new RuntimeException("request error");
    }

    @Override
    public Object queryDeviceLogs(List<Object> params) {
        String tuyaId = Objects.requireNonNull((String) params.get(0));
        JSONObject json = Objects.requireNonNull(JSONUtil.parseObj(params.get(1)));
        String type = Objects.requireNonNull(json.getStr("type"));
        Long startTime = Objects.requireNonNull(json.getLong("start_time"));
        Long endTime = Objects.requireNonNull(json.getLong("end_time"));
        Integer size = json.getInt("size") == null ? 20 : json.getInt("size");
        String codes = json.getStr("codes") == null ? "" : json.getStr("codes");
        Integer queryType = json.getInt("query_type") == null ? 1 : json.getInt("query_type");
        String startRowKey = json.getStr("start_row_key") == null ? "" : json.getStr("start_row_key");
        R r = tuyaAdapterFeign.queryDeviceLog(tuyaId, type, startTime, endTime, queryType, startRowKey, size, codes);
        String code = r.getOrDefault("code", 500).toString();
        if (code.equals("200")) {
            return r.get("data");
        }
        throw new RuntimeException("request error");
    }


    @Override
    public Object querySingleDeviceDetail(List<Object> params) {
        String id = (String) Objects.requireNonNull(params.get(0));
        R r = tuyaAdapterFeign.querySingleDeviceDetail(id);
        String code = r.getOrDefault("code", 500).toString();
        if (code.equals("200")) {
            return r.get("data");
        }
        throw new RuntimeException("request error");
    }

    @Override
    public List<Object> queryBatchDeviceDetail(List<Object> params) {
        if (params.get(1) instanceof List<?> deviceIds) {
            if (CollectionUtils.isEmpty(deviceIds)) return Collections.emptyList();
            R r = tuyaAdapterFeign.queryBatchDeviceDetail(String.join(",", deviceIds.stream().map(String::valueOf).toList()));
            return parseBatchResult(r);
        }
        throw new RuntimeException("request error");
    }

    @Override
    public Boolean isOnline(List<Object> params) {
        Object o = querySingleDeviceDetail(params);
        JSONObject jsonObject = JSONUtil.parseObj(o);
        return jsonObject.getBool("online", false);
    }

    @Override
    public String queryDeviceIp(List<Object> params) {
        Object o = querySingleDeviceDetail(params);
        JSONObject json = JSONUtil.parseObj(o);
        return json.getStr("ip");
    }

    @Override
    public List<String> queryAllOnlineDevice(List<Object> params) {
        R r = tuyaAdapterFeign.queryAllOnlineDevice();
        String code = r.getOrDefault("code", 500).toString();
        if (code.equals("200")) {
            Object o = r.get("data");
            if (o instanceof List<?> os) {
                return os.stream().map(obj -> (String) obj).toList();
            }
        }
        throw new RuntimeException("request error");
    }

    @Override
    public String queryDeviceCategory(List<Object> params) {
        Object o = querySingleDeviceDetail(params);
        JSONObject json = JSONUtil.parseObj(o);
        return json.getStr("category");
    }

    private List<Object> parseBatchResult(R r) {
        String code = r.getOrDefault("code", 500).toString();
        if (code.equals("200")) {
            Object o = r.get("data");
            if (o instanceof List<?> os) {
                return os.stream().map(obj -> (Object) obj).toList();
            }
        }
        throw new RuntimeException("request error");
    }
}
