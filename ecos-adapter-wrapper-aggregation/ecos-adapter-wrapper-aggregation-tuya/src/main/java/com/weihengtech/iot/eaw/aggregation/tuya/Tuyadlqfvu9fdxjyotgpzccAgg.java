package com.weihengtech.iot.eaw.aggregation.tuya;

import com.weihengtech.iot.eaw.aggregation.api.exception.AggregationRequestFailureException;
import com.weihengtech.iot.eaw.aggregation.api.tuya.ITuyadlqfvu9fdxjyotgpzccAgg;
import com.weihengtech.iot.eaw.aggregation.tuya.utils.RequestUtil;
import com.weihengtech.iot.eaw.repo.tuya.feign.TuyaAdapterFeign;
import com.weihengtech.pub.infra.utils.annotation.ThrowUniformly;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

@Component
@ThrowUniformly(AggregationRequestFailureException.class)
public class Tuyadlqfvu9fdxjyotgpzccAgg extends RequestUtil implements ITuyadlqfvu9fdxjyotgpzccAgg {
    public Tuyadlqfvu9fdxjyotgpzccAgg(TuyaAdapterFeign tuyaAdapterFeign) {
        super(tuyaAdapterFeign);
    }

    @Override
    public Boolean switchButton(List<Object> params) {
        String deviceId = (String) Objects.requireNonNull(params.get(0));
        Boolean switchFlag = (Boolean) Objects.requireNonNull(params.get(1));
        return sendCommand(deviceId, "switch", switchFlag);
    }

    @Override
    public Boolean leakageActionValue(List<Object> params) {
        String deviceId = (String) Objects.requireNonNull(params.get(0));
        Integer leakageActionValue = (Integer) Objects.requireNonNull(params.get(1));
        if (leakageActionValue < 0 || leakageActionValue > 999) return false;
        return sendCommand(deviceId, "leakage_action_value", leakageActionValue);
    }
}
