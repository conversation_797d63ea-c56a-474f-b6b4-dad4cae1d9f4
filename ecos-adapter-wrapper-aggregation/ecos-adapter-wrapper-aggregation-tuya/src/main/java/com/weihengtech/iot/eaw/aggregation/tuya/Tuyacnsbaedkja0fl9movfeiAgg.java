package com.weihengtech.iot.eaw.aggregation.tuya;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.weihengtech.iot.eaw.aggregation.api.exception.AggregationRequestFailureException;
import com.weihengtech.iot.eaw.aggregation.api.tuya.ITuyacnsbaedkja0fl9movfeiAgg;
import com.weihengtech.iot.eaw.repo.tuya.feign.TuyaAdapterFeign;
import com.weihengtech.iot.eaw.repo.tuya.model.dto.TuyaReadDeviceWithFuncDto;
import com.weihengtech.iot.eaw.repo.tuya.model.dto.TuyaSpeedupDeviceDto;
import com.weihengtech.iot.eaw.repo.tuya.model.dto.TuyaUpgradeDeviceDto;
import com.weihengtech.iot.eaw.repo.tuya.model.dto.TuyaWriteDeviceDto;
import com.weihengtech.pub.infra.resp.model.R;
import com.weihengtech.pub.infra.utils.annotation.ThrowUniformly;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

import static com.weihengtech.iot.eaw.aggregation.tuya.utils.ResponseUtil.isSuccessJSON;

@Component
@ThrowUniformly(AggregationRequestFailureException.class)
public class Tuyacnsbaedkja0fl9movfeiAgg implements ITuyacnsbaedkja0fl9movfeiAgg {

    private final TuyaAdapterFeign tuyaAdapterFeign;

    public Tuyacnsbaedkja0fl9movfeiAgg(TuyaAdapterFeign tuyaAdapterFeign) {
        this.tuyaAdapterFeign = tuyaAdapterFeign;
    }

    @Override
    public Boolean reset(List<Object> params) {
        R r = tuyaAdapterFeign.reset((String) params.get(0));
        String code = r.getOrDefault("code", 500).toString();
        return code.equals("200");
    }
    @Override
    public Boolean upgrade(List<Object> params) {
        String deviceId = Objects.requireNonNull((String) params.get(0));
        JSONObject json = Objects.requireNonNull(JSONUtil.parseObj(params.get(1)));
        TuyaUpgradeDeviceDto tuyaUpgradeDeviceDto = new TuyaUpgradeDeviceDto();
        tuyaUpgradeDeviceDto.setDeviceId(deviceId);
        tuyaUpgradeDeviceDto.setUrl(json.getStr("url"));
        tuyaUpgradeDeviceDto.setLen(json.getLong("len"));
        R r = tuyaAdapterFeign.upgrade(tuyaUpgradeDeviceDto);
        return isSuccessJSON(r);
    }

    @Override
    public List<Integer> transportRead(List<Object> params) {
        String deviceId = Objects.requireNonNull((String) params.get(0));
        JSONObject json = Objects.requireNonNull(JSONUtil.parseObj(params.get(1)));
        Integer functionCode = Objects.requireNonNull(json.getInt("func_code"));
        Integer slaveId = Objects.requireNonNull(json.getInt("slave_id"));
        Integer start = Objects.requireNonNull(json.getInt("start"));
        Integer length = Objects.requireNonNull(json.getInt("len"));
        TuyaReadDeviceWithFuncDto tuyaReadDeviceWithFuncDto = new TuyaReadDeviceWithFuncDto();
        tuyaReadDeviceWithFuncDto.setFuncCode(functionCode);
        tuyaReadDeviceWithFuncDto.setDeviceId(deviceId);
        tuyaReadDeviceWithFuncDto.setSlaveId(slaveId);
        tuyaReadDeviceWithFuncDto.setStartAddress(start);
        tuyaReadDeviceWithFuncDto.setLen(length);
        R r = tuyaAdapterFeign.readWithFuncCode(tuyaReadDeviceWithFuncDto);
        if (isSuccessJSON(r)) {
            Object o = r.get("result");
            if (o instanceof List<?> os) {
                return os.stream().map(obj -> (Integer) obj).toList();
            }
        }
        throw new RuntimeException("request failed");
    }

    @Override
    public Boolean transportWrite(List<Object> params) {
        String deviceId = Objects.requireNonNull((String) params.get(0));
        JSONObject json = Objects.requireNonNull(JSONUtil.parseObj(params.get(1)));
        Integer slaveId = Objects.requireNonNull(json.getInt("slave_id"));
        Integer start = Objects.requireNonNull(json.getInt("start"));
        Integer length = Objects.requireNonNull(json.getInt("len"));
        List<Integer> values = Objects.requireNonNull(json.getBeanList("values", Integer.class));
        TuyaWriteDeviceDto tuyaWriteDeviceDto = new TuyaWriteDeviceDto();
        tuyaWriteDeviceDto.setDeviceId(deviceId);
        tuyaWriteDeviceDto.setSlaveId(slaveId);
        tuyaWriteDeviceDto.setStartAddress(start);
        tuyaWriteDeviceDto.setLen(length);
        tuyaWriteDeviceDto.setValues(values);
        R r = tuyaAdapterFeign.write(tuyaWriteDeviceDto);
        return isSuccessJSON(r);
    }
    @Override
    public Boolean speedup(List<Object> params) {
        String deviceId = Objects.requireNonNull((String) params.get(0));
        TuyaSpeedupDeviceDto tuyaSpeedupDeviceDto = new TuyaSpeedupDeviceDto();
        tuyaSpeedupDeviceDto.setDeviceId(deviceId);
        R r = tuyaAdapterFeign.speedup(tuyaSpeedupDeviceDto);
        return r.getOrDefault("code", 500).toString().equals("200");
    }
}
