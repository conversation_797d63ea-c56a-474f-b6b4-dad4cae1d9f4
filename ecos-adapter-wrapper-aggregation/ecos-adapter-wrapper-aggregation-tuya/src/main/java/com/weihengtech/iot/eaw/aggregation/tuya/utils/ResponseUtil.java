package com.weihengtech.iot.eaw.aggregation.tuya.utils;

import com.weihengtech.pub.infra.resp.model.R;

public class ResponseUtil {
    public static Boolean isSuccessJSON(R r) {
        final String success = "SUCCESS";
        final String pending = "PENDING";
        final String failedResp = "false";
        String code = r.getOrDefault("code", 500).toString();
        String state = r.getOrDefault("state", "FAILURE").toString();
        String result = r.getOrDefault("result", "false").toString();
        return code.equals("200") && (pending.equals(state) || (success.equals(state) && !result.equals(failedResp)));
    }
}
