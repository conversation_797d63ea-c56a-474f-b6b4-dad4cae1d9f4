package com.weihengtech.iot.eaw.aggregation.tuya;

import com.weihengtech.iot.eaw.aggregation.api.exception.AggregationRequestFailureException;
import com.weihengtech.iot.eaw.aggregation.api.tuya.ITuyadlqyo9fwxtdAgg;
import com.weihengtech.iot.eaw.repo.tuya.feign.TuyaAdapterFeign;
import com.weihengtech.pub.infra.utils.annotation.ThrowUniformly;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

@Component
@ThrowUniformly(AggregationRequestFailureException.class)
public class Tuyadlqyo9fwxtdAgg extends Tuyadlqfvu9fdxjyotgpzccAgg implements ITuyadlqyo9fwxtdAgg {

    private final Tuyaczo9pyieorAgg tuyaczo9pyieorAgg;

    public Tuyadlqyo9fwxtdAgg(TuyaAdapterFeign tuyaAdapterFeign, Tuyaczo9pyieorAgg tuyaczo9pyieorAgg) {
        super(tuyaAdapterFeign);
        this.tuyaczo9pyieorAgg = tuyaczo9pyieorAgg;
    }

    @Override
    public Boolean relayStatus(List<Object> params) {
        return tuyaczo9pyieorAgg.relayStatus(params);
    }

    @Override
    public Boolean selfChecking(List<Object> params) {
        String deviceId = (String) Objects.requireNonNull(params.get(0));
        Boolean selfButton = (Boolean) Objects.requireNonNull(params.get(1));
        return sendCommand(deviceId, "self_checking", selfButton);
    }
}
