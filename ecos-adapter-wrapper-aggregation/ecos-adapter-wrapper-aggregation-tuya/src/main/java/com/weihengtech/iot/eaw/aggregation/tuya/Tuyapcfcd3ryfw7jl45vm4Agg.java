package com.weihengtech.iot.eaw.aggregation.tuya;

import com.weihengtech.iot.eaw.aggregation.api.exception.AggregationRequestFailureException;
import com.weihengtech.iot.eaw.aggregation.api.tuya.ITuyapcfcd3ryfw7jl45vm4Agg;
import com.weihengtech.iot.eaw.repo.tuya.feign.TuyaAdapterFeign;
import com.weihengtech.pub.infra.utils.annotation.ThrowUniformly;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

@Component
@ThrowUniformly(AggregationRequestFailureException.class)
public class Tuyapcfcd3ryfw7jl45vm4Agg extends Tuyacz0faj5nss7aejoy6tAgg implements ITuyapcfcd3ryfw7jl45vm4Agg {

    public Tuyapcfcd3ryfw7jl45vm4Agg(TuyaAdapterFeign tuyaAdapterFeign) {
        super(tuyaAdapterFeign);
    }

    @Override
    public Boolean switchButton2(List<Object> params) {
        String deviceId = (String) Objects.requireNonNull(params.get(0));
        Boolean switchFlag = (Boolean) Objects.requireNonNull(params.get(1));
        return sendCommand(deviceId, "switch_2", switchFlag);
    }

    @Override
    public Boolean countdown2(List<Object> params) {
        String deviceId = (String) Objects.requireNonNull(params.get(0));
        Integer countdownSec = (Integer) Objects.requireNonNull(params.get(1));
        return sendCommand(deviceId, "countdown_2", countdownSec);
    }
}
