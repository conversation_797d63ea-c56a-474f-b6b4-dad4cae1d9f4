package com.weihengtech.iot.eaw.aggregation.tuya;

import com.weihengtech.iot.eaw.aggregation.api.exception.AggregationRequestFailureException;
import com.weihengtech.iot.eaw.aggregation.api.tuya.ITuyaczxh5s5aplv0ewstyxAgg;
import com.weihengtech.iot.eaw.repo.tuya.feign.TuyaAdapterFeign;
import com.weihengtech.pub.infra.utils.annotation.ThrowUniformly;
import org.springframework.stereotype.Component;

@Component
@ThrowUniformly(AggregationRequestFailureException.class)
public class Tuyaczxh5s5aplv0ewstyxAgg  extends Tuyacz0faj5nss7aejoy6tAgg implements ITuyaczxh5s5aplv0ewstyxAgg {
    public Tuyaczxh5s5aplv0ewstyxAgg(TuyaAdapterFeign tuyaAdapterFeign) {
        super(tuyaAdapterFeign);
    }
}
