package com.weihengtech.iot.eaw.aggregation.tuya;

import com.weihengtech.iot.eaw.aggregation.api.exception.AggregationRequestFailureException;
import com.weihengtech.iot.eaw.aggregation.api.tuya.ITuyacz0faj5nss7aejoy6tAgg;
import com.weihengtech.iot.eaw.repo.tuya.feign.TuyaAdapterFeign;
import com.weihengtech.pub.infra.utils.annotation.ThrowUniformly;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

@Component
@ThrowUniformly(AggregationRequestFailureException.class)
public class Tuyacz0faj5nss7aejoy6tAgg extends Tuyaczo9pyieorAgg implements ITuyacz0faj5nss7aejoy6tAgg {

    public Tuyacz0faj5nss7aejoy6tAgg(TuyaAdapterFeign tuyaAdapterFeign) {
        super(tuyaAdapterFeign);
    }

    @Override
    public Boolean cycleTime(List<Object> params) {
        String deviceId = (String) Objects.requireNonNull(params.get(0));
        String cycleTime = (String) Objects.requireNonNull(params.get(1));
        return sendCommand(deviceId, "cycle_time", cycleTime);
    }

    @Override
    public Boolean randomTime(List<Object> params) {
        String deviceId = (String) Objects.requireNonNull(params.get(0));
        String randomTime = (String) Objects.requireNonNull(params.get(1));
        return sendCommand(deviceId, "random_time", randomTime);
    }

    @Override
    public Boolean overchargeSwitch(List<Object> params) {
        String deviceId = (String) Objects.requireNonNull(params.get(0));
        Boolean overchargeSwitch = (Boolean) Objects.requireNonNull(params.get(1));
        return sendCommand(deviceId, "overcharge_switch", overchargeSwitch);
    }
}
