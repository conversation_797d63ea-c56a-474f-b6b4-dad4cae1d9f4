package com.weihengtech.iot.eaw.aggregation.tuya;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.weihengtech.iot.eaw.aggregation.api.exception.AggregationRequestFailureException;
import com.weihengtech.iot.eaw.aggregation.api.tuya.ITuyaczo9pyieorAgg;
import com.weihengtech.iot.eaw.aggregation.tuya.utils.RequestUtil;
import com.weihengtech.iot.eaw.repo.tuya.feign.TuyaAdapterFeign;
import com.weihengtech.pub.infra.utils.annotation.ThrowUniformly;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

@Component
@ThrowUniformly(AggregationRequestFailureException.class)
public class Tuyaczo9pyieorAgg extends RequestUtil implements ITuyaczo9pyieorAgg {

    public Tuyaczo9pyieorAgg(TuyaAdapterFeign tuyaAdapterFeign) {
        super(tuyaAdapterFeign);
    }

    @Override
    public Boolean switchButton(List<Object> params) {
        String deviceId = (String) Objects.requireNonNull(params.get(0));
        Object o = Objects.requireNonNull(params.get(1));
        JSONObject json = JSONUtil.parseObj(o);
        Integer index = Objects.requireNonNull(json.getInt("index"));
        Boolean flag = Objects.requireNonNull(json.getBool("value"));
        return sendCommand(deviceId, "switch_"+index, flag);
    }

    @Override
    public Boolean countdown(List<Object> params) {
        String deviceId = (String) Objects.requireNonNull(params.get(0));
        Object o = Objects.requireNonNull(params.get(1));
        JSONObject json = JSONUtil.parseObj(o);
        Integer index = Objects.requireNonNull(json.getInt("index"));
        Integer countdownSec = Objects.requireNonNull(json.getInt("value"));
        return sendCommand(deviceId, "countdown_"+index, countdownSec);
    }

    @Override
    public Boolean relayStatus(List<Object> params) {
        String deviceId = (String) Objects.requireNonNull(params.get(0));
        String relayStatus = (String) Objects.requireNonNull(params.get(1));
        return switch (relayStatus) {
            case "last", "power_off", "power_on" -> sendCommand(deviceId, "relay_status", relayStatus);
            default -> false;
        };
    }

    @Override
    public Boolean childLock(List<Object> params) {
        String deviceId = (String) Objects.requireNonNull(params.get(0));
        Boolean switchFlag = (Boolean) Objects.requireNonNull(params.get(1));
        return sendCommand(deviceId, "child_lock", switchFlag);
    }

    @Override
    public Boolean lightMode(List<Object> params) {
        String deviceId = (String) Objects.requireNonNull(params.get(0));
        String lightMode = (String) Objects.requireNonNull(params.get(1));
        return switch (lightMode) {
            case "relay", "pos", "none" -> sendCommand(deviceId, "light_mode", lightMode);
            default -> throw new RuntimeException("不存在的light_mode");
        };
    }
}
