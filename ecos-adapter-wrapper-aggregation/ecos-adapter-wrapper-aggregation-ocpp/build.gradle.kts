group = rootProject.group
version = rootProject.ext.properties[project.name.replace("-","_")].toString()

publishing {
    publications {
        create<MavenPublication>("mavenJava") {
            versionMapping {
                usage("java-api") {
                    fromResolutionOf("runtimeClasspath")
                }
                usage("java-runtime") {
                    fromResolutionResult()
                }
            }
            artifactId = project.name
            from(components["java"])
        }
    }
    repositories {
        maven {
            val release = uri("http://nexus.dev.weiheng-tech.com/repository/maven-releases/")
            val snapshot = uri("http://nexus.dev.weiheng-tech.com/repository/maven-snapshots/")
            url = if (version.toString().endsWith("SNAPSHOT")) snapshot else release
            isAllowInsecureProtocol = true
            credentials {
                username = rootProject.ext.properties["username"].toString()
                password = rootProject.ext.properties["password"].toString()
            }
        }
    }
}

dependencies {
    implementation("com.weihengtech.iot:ecos-adapter-wrapper-repo-ocpp:17.0.1.0-20250507.065011-7")
    implementation("com.weihengtech.iot:ecos-adapter-wrapper-aggregation-api:17.0.1.0-20250902.033531-14")
}