package com.weihengtech.iot.eaw.aggregation.ocpp;

import cn.hutool.core.map.MapUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.weihengtech.iot.eaw.aggregation.api.exception.AggregationRequestFailureException;
import com.weihengtech.iot.eaw.aggregation.api.ocpp.IOcppAgg;
import com.weihengtech.iot.eaw.repo.ocpp.consts.OcppCpResetTypeEnum;
import com.weihengtech.iot.eaw.repo.ocpp.feign.OcppAdapterFeign;
import com.weihengtech.iot.eaw.repo.ocpp.model.dto.*;
import com.weihengtech.pub.infra.resp.model.R;
import com.weihengtech.pub.infra.utils.annotation.ThrowUniformly;

import java.math.BigDecimal;
import java.util.*;

@ThrowUniformly(AggregationRequestFailureException.class)
public class OcppChargePointAgg implements IOcppAgg {

    private final OcppAdapterFeign ocppAdapterFeign;

    public OcppChargePointAgg(OcppAdapterFeign ocppAdapterFeign) {
        this.ocppAdapterFeign = ocppAdapterFeign;
    }

    @Override
    public Boolean chargePointReset(List<Object> params) {
        String id = (String) Objects.requireNonNull(params.get(0));
        String resetType = (String) Objects.requireNonNull(params.get(1));
        OcppCpResetTypeEnum ocppCpResetTypeEnum = OcppCpResetTypeEnum.valueOf(resetType);
        OcppCpResetDto ocppCpResetDto = new OcppCpResetDto();
        ocppCpResetDto.setResetType(ocppCpResetTypeEnum);
        R r = ocppAdapterFeign.cpReset(id, ocppCpResetDto);
        return new BigDecimal(r.getOrDefault("code", 500).toString()).intValue() == 200;
    }

    @Override
    public Boolean startCharging(List<Object> params) {
        String id = (String) Objects.requireNonNull(params.get(0));
        Integer transactionId  = (Integer) Objects.requireNonNull(params.get(1));
        R r = ocppAdapterFeign.cpChargeStart(id, transactionId);
        return new BigDecimal(r.getOrDefault("code", 500).toString()).intValue() == 200;
    }

    @Override
    public Boolean stopCharging(List<Object> params) {
        String id = (String) Objects.requireNonNull(params.get(0));
        Integer transactionId = (Integer) Objects.requireNonNull(params.get(1));
        R r = ocppAdapterFeign.cpChargeStop(id, transactionId);
        return new BigDecimal(r.getOrDefault("code", 500).toString()).intValue() == 200;
    }

    @Override
    public Object chargePointStatus(List<Object> params) {
        String id = (String) Objects.requireNonNull(params.get(0));
        R r = ocppAdapterFeign.cpStatus(id);
        if (new BigDecimal(r.getOrDefault("code", 500).toString()).intValue() == 200) {
            Object data = Objects.requireNonNull(r.get("data"));
            JSONObject obj = JSONUtil.parseObj(data);
            Map<String, List<Map<String, Object>>> result = new HashMap<>(4);
            result.put("connectors", obj.keySet().stream().map(k -> MapUtil.<String, Object>builder()
                    .put("id", k)
                    .put("status", JSONUtil.parseObj(obj.getObj(k)).getStr("Status", "Unknown"))
                    .build()).toList());
            return result;
        }
        throw new RuntimeException("request error");
    }

    @Override
    public Object chargePointInfo(List<Object> params) {
        String id = (String) Objects.requireNonNull(params.get(0));
        R r = ocppAdapterFeign.cpInfo(id);
        if (new BigDecimal(r.getOrDefault("code", 500).toString()).intValue() == 200) {
            return Objects.requireNonNull(r.get("data"));
        }
        throw new RuntimeException("request error");
    }

    @Override
    public Boolean addCard(List<Object> params) {
        String id = (String) Objects.requireNonNull(params.get(0));
        String cardId = (String) Objects.requireNonNull(params.get(1));
        OcppCardAddDto ocppCardAddDto = new OcppCardAddDto();
        ocppCardAddDto.setCardId(cardId);
        R r = ocppAdapterFeign.cardAdd(id, ocppCardAddDto);
        if (new BigDecimal(r.getOrDefault("code", 500).toString()).intValue() == 200) {
            return true;
        }
        throw new RuntimeException("request error");
    }

    @Override
    public Boolean removeCards(List<Object> params) {
        String id = (String) Objects.requireNonNull(params.get(0));
        JSONArray cards =  JSONUtil.parseArray(Objects.requireNonNull(params.get(1)));
        OcppCardRemoveDto ocppCardRemoveDto = new OcppCardRemoveDto();
        ocppCardRemoveDto.setCardIds(cards.toList(String.class));
        R r = ocppAdapterFeign.cardRemove(id, ocppCardRemoveDto);
        if (new BigDecimal(r.getOrDefault("code", 500).toString()).intValue() == 200) {
            return true;
        }
        throw new RuntimeException("request error");
    }

    @Override
    public Boolean coverCards(List<Object> params) {
        String id = (String) Objects.requireNonNull(params.get(0));
        if (Objects.requireNonNull(params.get(1)) instanceof List<?> cards) {
            OcppCardCoverDto ocppCardCoverDto = new OcppCardCoverDto();
            ocppCardCoverDto.setCardIds(cards.parallelStream().map(String::valueOf).toList());
            R r = ocppAdapterFeign.cardCover(id, ocppCardCoverDto);
            return new BigDecimal(r.getOrDefault("code", 500).toString()).intValue() == 200;
        }
        throw new RuntimeException("request error");
    }

    @Override
    public Object queryCardsList(List<Object> params) {
        String id = (String) Objects.requireNonNull(params.get(0));
        R r = ocppAdapterFeign.cardList(id);
        if (new BigDecimal(r.getOrDefault("code", 500).toString()).intValue() == 200) {
            return r.get("data");
        }
        throw new RuntimeException("request error");
    }

    @Override
    public Boolean chargePointDefaultProfile(List<Object> params) {
        String id = (String) Objects.requireNonNull(params.get(0));
        JSONObject json = JSONUtil.parseObj(Objects.requireNonNull(params.get(1)));
        JSONArray periodArray = json.getJSONArray("chargeSchedulePeriods");
        List<OcppCpDefaultProfileDto.ChargingSchedulePeriod> periods = new ArrayList<>();
        periodArray.forEach(o -> {
            JSONObject obj = JSONUtil.parseObj(o);
            OcppCpDefaultProfileDto.ChargingSchedulePeriod chargingSchedulePeriod = new OcppCpDefaultProfileDto.ChargingSchedulePeriod();
            chargingSchedulePeriod.setStartPeriod(1);
            chargingSchedulePeriod.setLimit(obj.getLong("limit"));
            chargingSchedulePeriod.setNumberPhases(obj.getInt("numberPhases"));
            periods.add(chargingSchedulePeriod);
        });
        if (periods.isEmpty()) throw new RuntimeException("param error");
        OcppCpDefaultProfileDto ocppCpDefaultProfileDto = new OcppCpDefaultProfileDto();
        OcppCpDefaultProfileDto.ChargingSchedule chargingSchedule = new OcppCpDefaultProfileDto.ChargingSchedule();
        chargingSchedule.setChargingSchedulePeriod(periods);
        chargingSchedule.setChargingRateUnit(json.getStr("chargingRateUnit"));
        ocppCpDefaultProfileDto.setChargingSchedule(chargingSchedule);
        R r = ocppAdapterFeign.cpDefaultProfile(id, ocppCpDefaultProfileDto);
        return new BigDecimal(r.getOrDefault("code", 500).toString()).intValue() == 200;
    }

    @Override
    public Boolean chargePointMaxProfile(List<Object> params) {
        String id = (String) Objects.requireNonNull(params.get(0));
        JSONObject json = JSONUtil.parseObj(Objects.requireNonNull(params.get(1)));
        Long limit = Objects.requireNonNull(json.getLong("limit"));
        String unit = Objects.requireNonNull(json.getStr("unit"));
        Integer numberPhases = Objects.requireNonNull(json.getInt("numberPhases"));
        OcppCpMaxProfileDto ocppCpMaxProfileDto = new OcppCpMaxProfileDto();
        ocppCpMaxProfileDto.setLimit(limit);
        ocppCpMaxProfileDto.setUnit(unit);
        ocppCpMaxProfileDto.setNumberPhases(numberPhases);
        R r = ocppAdapterFeign.cpMaxProfile(id, ocppCpMaxProfileDto);
        return new BigDecimal(r.getOrDefault("code", 500).toString()).intValue() == 200;
    }

    @Override
    public Object chargePointFirmwareStatus(List<Object> params) {
        String id = (String) Objects.requireNonNull(params.get(0));
        R r = ocppAdapterFeign.cpFirmwareStatus(id);
        if (new BigDecimal(r.getOrDefault("code", 500).toString()).intValue() == 200) {
            return r.get("data");
        }
        throw new RuntimeException("request error");
    }

    @Override
    public Boolean chargePointFirmwareUpdate(List<Object> params) {
        String id = (String) Objects.requireNonNull(params.get(0));
        JSONObject json = JSONUtil.parseObj(Objects.requireNonNull(params.get(1)));
        String location = Objects.requireNonNull(json.getStr("location"));
        Integer retries = json.getInt("retries", 3);
        Integer retryInterval = json.getInt("retry_interval", 10);
        Long retrieveDate = json.getLong("retrieve_date", System.currentTimeMillis());
        OcppUpdateFirmwareDto ocppUpdateFirmwareDto = new OcppUpdateFirmwareDto();
        ocppUpdateFirmwareDto.setLocation(location);
        ocppUpdateFirmwareDto.setRetries(retries);
        ocppUpdateFirmwareDto.setRetryInterval(retryInterval);
        ocppUpdateFirmwareDto.setRetrieveDate(retrieveDate);
        R r = ocppAdapterFeign.cpFirmwareUpdate(id, ocppUpdateFirmwareDto);
        return new BigDecimal(r.getOrDefault("code", 500).toString()).intValue() == 200;
    }

    @Override
    public Boolean chargePointShutdownLoadBalance(List<Object> params) {
        String id = (String) Objects.requireNonNull(params.get(0));
        OcppDataTransferDto ocppDataTransferDto = new OcppDataTransferDto();
        ocppDataTransferDto.setData("0142040A010000");
        ocppDataTransferDto.setMessageId("templateInfo");
        ocppDataTransferDto.setVendorId("EN+");
        R r = ocppAdapterFeign.cpDataTransfer(id, ocppDataTransferDto);
        return new BigDecimal(r.getOrDefault("code", 500).toString()).intValue() == 200;
    }

    @Override
    public Boolean chargePointGetDiagnostics(List<Object> params) {
        String id = (String) Objects.requireNonNull(params.get(0));
        JSONObject json = JSONUtil.parseObj(Objects.requireNonNull(params.get(1)));
        Long startTime = json.getLong("start_time");
        Long endTime = json.getLong("end_time");
        R r;
        OcppDiagnosticsDto ocppDiagnosticsDto = new OcppDiagnosticsDto();
        if (startTime != null && endTime != null) {
            ocppDiagnosticsDto.setStartTime(startTime);
            ocppDiagnosticsDto.setEndTime(endTime);
        }
        r = ocppAdapterFeign.cpDiagnostics(id, ocppDiagnosticsDto);
        return new BigDecimal(r.getOrDefault("code", 500).toString()).intValue() == 200;
    }

    @Override
    public Object getConfiguration(List<Object> params) {
        String id = (String) Objects.requireNonNull(params.get(0));
        String keys = (String) params.get(1);
        R r = ocppAdapterFeign.getConfiguration(id, keys);
        if (new BigDecimal(r.getOrDefault("code", 500).toString()).intValue() == 200) {
            return r.get("data");
        }
        throw new RuntimeException("request error");
    }

    @Override
    public Boolean updConfiguration(List<Object> params) {
        String id = (String) Objects.requireNonNull(params.get(0));
        JSONObject json = JSONUtil.parseObj(Objects.requireNonNull(params.get(1)));
        String key = json.getStr("key");
        String value = json.getStr("value");
        R r;
        OcppConfigurationDTO param = new OcppConfigurationDTO();
        if (key != null && value != null) {
            param.setKey(key);
            param.setValue(value);
        }
        r = ocppAdapterFeign.updConfiguration(id, param);
        return new BigDecimal(r.getOrDefault("code", 500).toString()).intValue() == 200;
    }

    @Override
    public Boolean chargePointDataTransfer(List<Object> params) {
        String id = (String) Objects.requireNonNull(params.get(0));
        String data = (String) Objects.requireNonNull(params.get(1));
        OcppDataTransferDto ocppDataTransferDto = new OcppDataTransferDto();
        ocppDataTransferDto.setData(data);
        ocppDataTransferDto.setMessageId("templateInfo");
        ocppDataTransferDto.setVendorId("EN+");
        R r = ocppAdapterFeign.cpDataTransfer(id, ocppDataTransferDto);
        return new BigDecimal(r.getOrDefault("code", 500).toString()).intValue() == 200;
    }
}
