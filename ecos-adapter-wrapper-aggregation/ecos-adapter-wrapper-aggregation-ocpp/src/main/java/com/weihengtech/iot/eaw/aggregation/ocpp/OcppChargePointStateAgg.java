package com.weihengtech.iot.eaw.aggregation.ocpp;

import com.weihengtech.iot.eaw.aggregation.api.IStateAgg;
import com.weihengtech.iot.eaw.aggregation.api.exception.AggregationRequestFailureException;
import com.weihengtech.iot.eaw.repo.ocpp.feign.OcppAdapterFeign;
import com.weihengtech.pub.infra.resp.model.R;
import com.weihengtech.pub.infra.utils.annotation.ThrowUniformly;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

@ThrowUniformly(AggregationRequestFailureException.class)
public class OcppChargePointStateAgg implements IStateAgg {

    private final OcppAdapterFeign ocppAdapterFeign;

    public OcppChargePointStateAgg(OcppAdapterFeign ocppAdapterFeign) {
        this.ocppAdapterFeign = ocppAdapterFeign;
    }

    @Override
    public Boolean isOnline(List<Object> params) {
        String id = (String) Objects.requireNonNull(params.get(0));
        R r = ocppAdapterFeign.cpIsOnline(id);
        if (new BigDecimal(r.getOrDefault("code", 500).toString()).intValue() == 200) {
            return (Boolean)r.get("data");
        }
        throw new RuntimeException("request error");
    }

    @Override
    public List<String> queryAllOnlineDevice(List<Object> params) {
        R r = ocppAdapterFeign.cpOnline();
        if (new BigDecimal(r.getOrDefault("code", 500).toString()).intValue() == 200) {
            if (Objects.requireNonNull(r.get("data")) instanceof List<?> devices) {
                return devices.stream().map(String::valueOf).toList();
            } else {
                return Collections.emptyList();
            }
        }
        throw new RuntimeException("request error");
    }
}
